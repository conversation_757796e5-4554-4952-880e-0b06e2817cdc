<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 社区吐槽</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info">社区吐槽</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 搜索和筛选 -->
            <div class="search-filter-section">
                <div class="search-container">
                    <input type="text" class="search-input" placeholder="搜索帖子内容..." onkeyup="searchPosts()">
                    <i class="fas fa-search search-icon"></i>
                </div>
                <div class="filter-tabs">
                    <div class="filter-tab active" onclick="filterPosts('all')">全部</div>
                    <div class="filter-tab" onclick="filterPosts('hot')">热门</div>
                    <div class="filter-tab" onclick="filterPosts('new')">最新</div>
                </div>
            </div>

            <!-- 帖子列表 -->
            <div class="posts-container">
                <div class="post-item" data-category="hot" onclick="openPostDetail(1)">
                    <div class="post-image">
                        <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="时尚趋势讨论">
                        <div class="post-badge hot">热门</div>
                    </div>
                    <div class="post-content">
                        <h3 class="post-title">2024年春季时尚趋势大讨论</h3>
                        <p class="post-excerpt">今年春季的时尚趋势真的让人眼前一亮，从复古回潮到未来主义，各种风格百花齐放...</p>
                        <div class="post-meta">
                            <div class="post-stats">
                                <span><i class="fas fa-eye"></i> 1.2k</span>
                                <span><i class="fas fa-comment"></i> 89</span>
                                <span><i class="fas fa-heart"></i> 156</span>
                            </div>
                            <div class="post-time">2小时前</div>
                        </div>
                    </div>
                </div>

                <div class="post-item" data-category="new" onclick="openPostDetail(2)">
                    <div class="post-image">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="摄影技巧分享">
                        <div class="post-badge new">最新</div>
                    </div>
                    <div class="post-content">
                        <h3 class="post-title">人像摄影的光影技巧分享</h3>
                        <p class="post-excerpt">分享一些人像摄影中关于光影运用的小技巧，希望对大家有帮助...</p>
                        <div class="post-meta">
                            <div class="post-stats">
                                <span><i class="fas fa-eye"></i> 456</span>
                                <span><i class="fas fa-comment"></i> 23</span>
                                <span><i class="fas fa-heart"></i> 67</span>
                            </div>
                            <div class="post-time">30分钟前</div>
                        </div>
                    </div>
                </div>

                <div class="post-item" data-category="hot" onclick="openPostDetail(3)">
                    <div class="post-image">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="模特经验分享">
                    </div>
                    <div class="post-content">
                        <h3 class="post-title">新手模特如何在镜头前更自然</h3>
                        <p class="post-excerpt">作为一名新手模特，刚开始面对镜头总是很紧张，经过一段时间的练习...</p>
                        <div class="post-meta">
                            <div class="post-stats">
                                <span><i class="fas fa-eye"></i> 892</span>
                                <span><i class="fas fa-comment"></i> 45</span>
                                <span><i class="fas fa-heart"></i> 123</span>
                            </div>
                            <div class="post-time">5小时前</div>
                        </div>
                    </div>
                </div>

                <div class="post-item" data-category="new" onclick="openPostDetail(4)">
                    <div class="post-image">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="时尚杂志评测">
                    </div>
                    <div class="post-content">
                        <h3 class="post-title">本月时尚杂志质量评测</h3>
                        <p class="post-excerpt">这个月的几本时尚杂志都看完了，来给大家分享一下个人的感受和评价...</p>
                        <div class="post-meta">
                            <div class="post-stats">
                                <span><i class="fas fa-eye"></i> 234</span>
                                <span><i class="fas fa-comment"></i> 12</span>
                                <span><i class="fas fa-heart"></i> 34</span>
                            </div>
                            <div class="post-time">1天前</div>
                        </div>
                    </div>
                </div>

                <div class="post-item" data-category="hot" onclick="openPostDetail(5)">
                    <div class="post-image">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="摄影器材讨论">
                        <div class="post-badge hot">热门</div>
                    </div>
                    <div class="post-content">
                        <h3 class="post-title">摄影器材选择的那些坑</h3>
                        <p class="post-excerpt">入坑摄影这么多年，踩过不少器材选择的坑，今天来给大家分享一下经验...</p>
                        <div class="post-meta">
                            <div class="post-stats">
                                <span><i class="fas fa-eye"></i> 1.5k</span>
                                <span><i class="fas fa-comment"></i> 78</span>
                                <span><i class="fas fa-heart"></i> 234</span>
                            </div>
                            <div class="post-time">1天前</div>
                        </div>
                    </div>
                </div>

                <div class="post-item" data-category="new" onclick="openPostDetail(6)">
                    <div class="post-image">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="时尚搭配">
                    </div>
                    <div class="post-content">
                        <h3 class="post-title">春季穿搭灵感分享</h3>
                        <p class="post-excerpt">春天来了，是时候换掉厚重的冬装了，分享一些春季穿搭的小心得...</p>
                        <div class="post-meta">
                            <div class="post-stats">
                                <span><i class="fas fa-eye"></i> 567</span>
                                <span><i class="fas fa-comment"></i> 34</span>
                                <span><i class="fas fa-heart"></i> 89</span>
                            </div>
                            <div class="post-time">2天前</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more">
                <button class="btn btn-secondary" onclick="loadMorePosts()">
                    <i class="fas fa-plus"></i>
                    加载更多
                </button>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item" onclick="FashionApp.goToHome()">
                <span>首页</span>
            </div>
            <div class="nav-item" onclick="FashionApp.goToModels()">
                <span>模特</span>
            </div>
            <div class="nav-item" onclick="FashionApp.goToRankings()">
                <span>热榜</span>
            </div>
            <div class="nav-item active">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        function searchPosts() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            const posts = document.querySelectorAll('.post-item');

            posts.forEach(post => {
                const title = post.querySelector('.post-title').textContent.toLowerCase();
                const excerpt = post.querySelector('.post-excerpt').textContent.toLowerCase();

                if (title.includes(searchTerm) || excerpt.includes(searchTerm)) {
                    post.style.display = 'block';
                } else {
                    post.style.display = 'none';
                }
            });
        }

        function filterPosts(category) {
            // 更新标签状态
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            // 筛选帖子
            const posts = document.querySelectorAll('.post-item');
            posts.forEach(post => {
                if (category === 'all') {
                    post.style.display = 'block';
                } else {
                    const postCategory = post.getAttribute('data-category');
                    if (postCategory === category) {
                        post.style.display = 'block';
                    } else {
                        post.style.display = 'none';
                    }
                }
            });
        }

        function openPostDetail(postId) {
            FashionApp.showToast(`打开帖子详情: ${postId}`, 'info');
            // 这里可以跳转到帖子详情页面
        }

        function loadMorePosts() {
            FashionApp.showToast('加载更多帖子...', 'info');
            // 这里可以加载更多帖子数据
        }
    </script>

    <style>
        .search-filter-section {
            margin-bottom: 20px;
        }

        .filter-tabs {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .filter-tab {
            padding: 8px 16px;
            background: var(--secondary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .filter-tab.active {
            background: var(--accent-dark);
            color: white;
            border-color: var(--accent-dark);
        }

        .filter-tab:hover {
            border-color: var(--accent-dark);
        }

        .posts-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }

        .post-item {
            background: var(--secondary-dark);
            border-radius: 15px;
            overflow: hidden;
            border: 1px solid var(--border-dark);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .post-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-dark);
        }

        .post-image {
            position: relative;
            height: 120px;
            overflow: hidden;
        }

        .post-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .post-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .post-badge.hot {
            background: #e74c3c;
        }

        .post-badge.new {
            background: #27ae60;
        }

        .post-content {
            padding: 15px;
        }

        .post-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .post-excerpt {
            font-size: 14px;
            color: var(--text-secondary-dark);
            line-height: 1.5;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .post-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .post-stats {
            display: flex;
            gap: 15px;
        }

        .post-stats span {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .post-stats i {
            font-size: 12px;
        }

        .post-time {
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .load-more {
            text-align: center;
            margin-top: 20px;
        }

        .load-more .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .post-stats {
                gap: 10px;
            }

            .post-stats span {
                font-size: 11px;
            }
        }
    </style>
</body>
</html>
