<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 模特详情</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">
                <button class="back-btn" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                模特详情
            </div>
            <div class="user-info">
                <button class="share-btn" onclick="shareModel()">
                    <i class="fas fa-share-alt"></i>
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 模特头部信息 -->
            <div class="model-header">
                <div class="model-cover">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="艾米丽">
                    <div class="model-overlay">
                        <div class="model-badge international">国际模特</div>
                        <div class="model-rating">
                            <i class="fas fa-star"></i>
                            <span>4.9</span>
                        </div>
                    </div>
                </div>
                
                <div class="model-basic-info">
                    <h1 class="model-name">艾米丽 (Emily)</h1>
                    <p class="model-specialty">清新自然风格，春季主题专家</p>
                    
                    <div class="model-stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">8</div>
                            <div class="stat-label">往期作品</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">2.3k</div>
                            <div class="stat-label">粉丝关注</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">4.9</div>
                            <div class="stat-label">评分</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">156</div>
                            <div class="stat-label">获赞数</div>
                        </div>
                    </div>

                    <div class="model-actions">
                        <button class="btn btn-primary follow-btn" onclick="toggleFollow()">
                            <i class="fas fa-plus"></i>
                            关注
                        </button>
                        <button class="btn btn-secondary message-btn" onclick="sendMessage()">
                            <i class="fas fa-envelope"></i>
                            私信
                        </button>
                    </div>
                </div>
            </div>

            <!-- 模特特点介绍 -->
            <div class="model-description">
                <h3 class="section-title">
                    <i class="fas fa-user"></i>
                    模特介绍
                </h3>
                <div class="description-content">
                    <p>艾米丽是一位来自欧洲的专业时尚模特，拥有5年的专业拍摄经验。她以清新自然的风格著称，特别擅长春季主题的拍摄。</p>
                    <p>她的作品风格多样，从清新的田园风到优雅的都市风，都能完美驾驭。艾米丽注重细节，善于通过表情和肢体语言传达情感，为每一次拍摄带来独特的魅力。</p>
                    
                    <div class="model-features">
                        <h4>专业特长</h4>
                        <div class="feature-tags">
                            <span class="feature-tag">清新自然</span>
                            <span class="feature-tag">春季主题</span>
                            <span class="feature-tag">田园风格</span>
                            <span class="feature-tag">情感表达</span>
                            <span class="feature-tag">细节把控</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 往期作品 -->
            <div class="model-works">
                <h3 class="section-title">
                    <i class="fas fa-camera"></i>
                    往期作品 (8期)
                </h3>
                
                <div class="works-list">
                    <div class="work-item" onclick="openMagazine('2024001')">
                        <div class="work-cover">
                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第001期">
                            <div class="work-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="work-info">
                            <h4 class="work-title">第001期 · 春日物语</h4>
                            <p class="work-magazine">时尚周刊</p>
                            <div class="work-meta">
                                <span class="work-date">2024-01-15</span>
                                <div class="work-stats">
                                    <span><i class="fas fa-eye"></i> 12.5k</span>
                                    <span><i class="fas fa-heart"></i> 2.3k</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="work-item" onclick="openMagazine('2023012')">
                        <div class="work-cover">
                            <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第012期">
                            <div class="work-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="work-info">
                            <h4 class="work-title">第012期 · 自然之美</h4>
                            <p class="work-magazine">美丽世界</p>
                            <div class="work-meta">
                                <span class="work-date">2023-12-20</span>
                                <div class="work-stats">
                                    <span><i class="fas fa-eye"></i> 10.2k</span>
                                    <span><i class="fas fa-heart"></i> 1.8k</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="work-item" onclick="openMagazine('2023010')">
                        <div class="work-cover">
                            <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第010期">
                            <div class="work-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="work-info">
                            <h4 class="work-title">第010期 · 田园诗意</h4>
                            <p class="work-magazine">风格指南</p>
                            <div class="work-meta">
                                <span class="work-date">2023-11-15</span>
                                <div class="work-stats">
                                    <span><i class="fas fa-eye"></i> 9.8k</span>
                                    <span><i class="fas fa-heart"></i> 1.6k</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="work-item" onclick="openMagazine('2023008')">
                        <div class="work-cover">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第008期">
                            <div class="work-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="work-info">
                            <h4 class="work-title">第008期 · 春季时尚</h4>
                            <p class="work-magazine">时尚周刊</p>
                            <div class="work-meta">
                                <span class="work-date">2023-10-10</span>
                                <div class="work-stats">
                                    <span><i class="fas fa-eye"></i> 8.9k</span>
                                    <span><i class="fas fa-heart"></i> 1.4k</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="work-item" onclick="openMagazine('2023006')">
                        <div class="work-cover">
                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第006期">
                            <div class="work-overlay">
                                <i class="fas fa-play"></i>
                            </div>
                        </div>
                        <div class="work-info">
                            <h4 class="work-title">第006期 · 清晨时光</h4>
                            <p class="work-magazine">摄影艺术</p>
                            <div class="work-meta">
                                <span class="work-date">2023-09-05</span>
                                <div class="work-stats">
                                    <span><i class="fas fa-eye"></i> 7.6k</span>
                                    <span><i class="fas fa-heart"></i> 1.2k</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="view-all-works">
                    <button class="btn btn-secondary" onclick="viewAllWorks()">
                        <i class="fas fa-th"></i>
                        查看全部作品
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <span>首页</span>
            </div>
            <div class="nav-item active">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        let isFollowing = false;

        function goBack() {
            FashionApp.showToast('返回模特列表', 'info');
        }

        function shareModel() {
            FashionApp.showModal('分享模特', `
                <div class="share-options">
                    <div class="share-item" onclick="shareToWeChat()">
                        <i class="fab fa-wechat"></i>
                        <span>微信</span>
                    </div>
                    <div class="share-item" onclick="shareToWeibo()">
                        <i class="fab fa-weibo"></i>
                        <span>微博</span>
                    </div>
                    <div class="share-item" onclick="copyLink()">
                        <i class="fas fa-link"></i>
                        <span>复制链接</span>
                    </div>
                </div>
            `);
        }

        function toggleFollow() {
            const followBtn = document.querySelector('.follow-btn');
            const icon = followBtn.querySelector('i');
            
            if (isFollowing) {
                followBtn.classList.remove('following');
                icon.className = 'fas fa-plus';
                followBtn.innerHTML = '<i class="fas fa-plus"></i> 关注';
                FashionApp.showToast('取消关注', 'info');
            } else {
                followBtn.classList.add('following');
                icon.className = 'fas fa-check';
                followBtn.innerHTML = '<i class="fas fa-check"></i> 已关注';
                FashionApp.showToast('关注成功', 'success');
            }
            isFollowing = !isFollowing;
        }

        function sendMessage() {
            FashionApp.showModal('发送私信', `
                <div class="message-form">
                    <h3>给艾米丽发送私信</h3>
                    <textarea class="message-input" placeholder="请输入您想说的话..." rows="4"></textarea>
                    <div class="message-actions">
                        <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                        <button class="btn btn-primary" onclick="sendPrivateMessage()">发送</button>
                    </div>
                </div>
            `);
        }

        function sendPrivateMessage() {
            const message = document.querySelector('.message-input').value;
            if (message.trim()) {
                FashionApp.showToast('私信发送成功', 'success');
                closeModal();
            } else {
                FashionApp.showToast('请输入消息内容', 'error');
            }
        }

        function openMagazine(magazineId) {
            FashionApp.showToast(`打开杂志: ${magazineId}`, 'info');
        }

        function viewAllWorks() {
            FashionApp.showToast('查看全部作品', 'info');
        }

        function shareToWeChat() {
            FashionApp.showToast('分享到微信', 'success');
            closeModal();
        }

        function shareToWeibo() {
            FashionApp.showToast('分享到微博', 'success');
            closeModal();
        }

        function copyLink() {
            FashionApp.showToast('链接已复制到剪贴板', 'success');
            closeModal();
        }

        function closeModal() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }
    </script>

    <style>
        .back-btn, .share-btn {
            background: none;
            border: none;
            color: var(--text-dark);
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .back-btn:hover, .share-btn:hover {
            background: var(--border-dark);
        }

        .model-header {
            margin-bottom: 25px;
        }

        .model-cover {
            position: relative;
            height: 250px;
            border-radius: 20px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .model-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .model-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 20px;
        }

        .model-badge {
            background: #3498db;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }

        .model-rating {
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 14px;
            font-weight: bold;
        }

        .model-rating i {
            color: #ffd700;
        }

        .model-name {
            font-size: 24px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .model-specialty {
            font-size: 16px;
            color: var(--text-secondary-dark);
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .model-stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: var(--secondary-dark);
            border-radius: 12px;
            padding: 15px 10px;
            text-align: center;
            border: 1px solid var(--border-dark);
        }

        .stat-number {
            font-size: 18px;
            font-weight: bold;
            color: var(--accent-dark);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .model-actions {
            display: flex;
            gap: 15px;
        }

        .follow-btn,
        .message-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 600;
        }

        .follow-btn.following {
            background: var(--border-dark);
            color: var(--text-secondary-dark);
        }

        .model-description,
        .model-works {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--border-dark);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            font-size: 18px;
            font-weight: bold;
            color: var(--text-dark);
        }

        .section-title i {
            color: var(--accent-dark);
        }

        .description-content p {
            color: var(--text-secondary-dark);
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .model-features {
            margin-top: 20px;
        }

        .model-features h4 {
            color: var(--text-dark);
            margin-bottom: 12px;
            font-size: 16px;
        }

        .feature-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .feature-tag {
            background: var(--primary-dark);
            color: var(--accent-dark);
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            border: 1px solid var(--accent-dark);
        }

        .works-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }

        .work-item {
            display: flex;
            gap: 15px;
            background: var(--primary-dark);
            border-radius: 12px;
            padding: 15px;
            border: 1px solid var(--border-dark);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .work-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-dark);
        }

        .work-cover {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .work-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .work-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .work-item:hover .work-overlay {
            opacity: 1;
        }

        .work-overlay i {
            color: white;
            font-size: 20px;
        }

        .work-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .work-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 5px;
            line-height: 1.3;
        }

        .work-magazine {
            font-size: 12px;
            color: var(--text-secondary-dark);
            margin-bottom: 8px;
        }

        .work-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .work-date {
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .work-stats {
            display: flex;
            gap: 12px;
        }

        .work-stats span {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            color: var(--text-secondary-dark);
        }

        .work-stats i {
            font-size: 11px;
        }

        .view-all-works {
            text-align: center;
        }

        .view-all-works .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        /* 模态框样式 */
        .share-options {
            display: flex;
            justify-content: space-around;
            padding: 20px 0;
        }

        .share-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            padding: 15px;
            border-radius: 10px;
            transition: background 0.3s ease;
        }

        .share-item:hover {
            background: var(--border-dark);
        }

        .share-item i {
            font-size: 24px;
            color: var(--accent-dark);
        }

        .share-item span {
            font-size: 12px;
            color: var(--text-dark);
        }

        .message-form {
            text-align: center;
        }

        .message-form h3 {
            color: var(--text-dark);
            margin-bottom: 20px;
        }

        .message-input {
            width: 100%;
            padding: 15px;
            border: 1px solid var(--border-dark);
            border-radius: 10px;
            background: var(--primary-dark);
            color: var(--text-dark);
            font-size: 14px;
            resize: vertical;
            margin-bottom: 20px;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--accent-dark);
        }

        .message-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }
    </style>
</body>
</html>
