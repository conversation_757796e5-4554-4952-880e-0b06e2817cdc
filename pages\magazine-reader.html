<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 杂志阅读</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">
                <button class="back-btn" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                第001期 · 春日物语
            </div>
            <div class="user-info">
                <button class="more-btn" onclick="showMoreOptions()">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 杂志封面 -->
            <div class="magazine-cover-section">
                <div class="cover-image">
                    <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="第001期封面">
                </div>
                <div class="cover-info">
                    <h2>第001期 · 春日物语</h2>
                    <p>模特：艾米丽 | 摄影师：张伟 | 发布时间：2024年1月15日</p>
                </div>
            </div>

            <!-- 标签页 -->
            <div class="content-tabs">
                <div class="tab active" onclick="switchTab('images')">
                    <i class="fas fa-images"></i>
                    <span>杂志图片</span>
                </div>
                <div class="tab" onclick="switchTab('videos')">
                    <i class="fas fa-video"></i>
                    <span>杂志视频</span>
                </div>
            </div>

            <!-- 图片内容 -->
            <div class="tab-content active" id="images-content">
                <div class="images-grid">
                    <div class="image-item" onclick="viewImage(1)">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="图片1">
                        <div class="image-overlay">
                            <i class="fas fa-search-plus"></i>
                        </div>
                    </div>
                    <div class="image-item" onclick="viewImage(2)">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="图片2">
                        <div class="image-overlay">
                            <i class="fas fa-search-plus"></i>
                        </div>
                    </div>
                    <div class="image-item" onclick="viewImage(3)">
                        <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="图片3">
                        <div class="image-overlay">
                            <i class="fas fa-search-plus"></i>
                        </div>
                    </div>
                    <div class="image-item" onclick="viewImage(4)">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="图片4">
                        <div class="image-overlay">
                            <i class="fas fa-search-plus"></i>
                        </div>
                    </div>
                    <div class="image-item" onclick="viewImage(5)">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="图片5">
                        <div class="image-overlay">
                            <i class="fas fa-search-plus"></i>
                        </div>
                    </div>
                    <div class="image-item" onclick="viewImage(6)">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="图片6">
                        <div class="image-overlay">
                            <i class="fas fa-search-plus"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 视频内容 -->
            <div class="tab-content" id="videos-content">
                <div class="videos-list">
                    <div class="video-item" onclick="playVideo(1)">
                        <div class="video-thumbnail">
                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="视频1">
                            <div class="play-button">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration">03:24</div>
                        </div>
                        <div class="video-info">
                            <h4>春日写真花絮</h4>
                            <p>拍摄现场的精彩瞬间</p>
                        </div>
                    </div>

                    <div class="video-item" onclick="playVideo(2)">
                        <div class="video-thumbnail">
                            <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="视频2">
                            <div class="play-button">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration">02:15</div>
                        </div>
                        <div class="video-info">
                            <h4>模特访谈</h4>
                            <p>艾米丽分享拍摄心得</p>
                        </div>
                    </div>

                    <div class="video-item" onclick="playVideo(3)">
                        <div class="video-thumbnail">
                            <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80" alt="视频3">
                            <div class="play-button">
                                <i class="fas fa-play"></i>
                            </div>
                            <div class="video-duration">01:45</div>
                        </div>
                        <div class="video-info">
                            <h4>造型解析</h4>
                            <p>春季时尚搭配技巧</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 高清下载悬浮按钮 -->
        <button class="download-btn" onclick="showDownloadOptions()">
            <i class="fas fa-download"></i>
            <span>高清下载</span>
        </button>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        // 模拟用户状态
        const isVIP = true; // 设为true来测试VIP功能
        const downloadRemaining = 15; // 剩余下载次数
        const bonusDownloads = 5; // 赠送下载次数

        function goBack() {
            FashionApp.showToast('返回杂志详情', 'info');
        }

        function showMoreOptions() {
            FashionApp.showModal('更多选项', `
                <div class="more-options">
                    <div class="option-item" onclick="shareMagazine()">
                        <i class="fas fa-share-alt"></i>
                        <span>分享杂志</span>
                    </div>
                    <div class="option-item" onclick="reportIssue()">
                        <i class="fas fa-flag"></i>
                        <span>举报问题</span>
                    </div>
                    <div class="option-item" onclick="addToFavorites()">
                        <i class="fas fa-heart"></i>
                        <span>添加收藏</span>
                    </div>
                </div>
            `);
        }

        function switchTab(tabName) {
            // 更新标签状态
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.getElementById(`${tabName}-content`).classList.add('active');
        }

        function viewImage(imageId) {
            FashionApp.showModal('图片预览', `
                <div class="image-preview">
                    <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="图片${imageId}">
                    <div class="image-actions">
                        <button class="btn btn-secondary" onclick="downloadImage(${imageId})">
                            <i class="fas fa-download"></i>
                            下载
                        </button>
                        <button class="btn btn-secondary" onclick="shareImage(${imageId})">
                            <i class="fas fa-share-alt"></i>
                            分享
                        </button>
                    </div>
                </div>
            `);
        }

        function playVideo(videoId) {
            if (!isVIP) {
                FashionApp.showModal('VIP专享', `
                    <div class="vip-required">
                        <div class="vip-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <h3>您还不是会员</h3>
                        <p>观看视频内容需要VIP会员权限</p>
                        <div class="vip-actions">
                            <button class="btn btn-primary" onclick="goToVIP()">购买VIP会员</button>
                            <button class="btn btn-secondary" onclick="buySingleIssue()">单独购买本期</button>
                        </div>
                        <div class="price-info">
                            <span>本期售价：¥15.00</span>
                        </div>
                    </div>
                `);
                return;
            }

            FashionApp.showToast(`播放视频 ${videoId}`, 'info');
            // 这里可以实现视频播放功能
        }

        function showDownloadOptions() {
            if (!isVIP) {
                FashionApp.showModal('下载权限', `
                    <div class="download-permission">
                        <div class="permission-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <h3>您还不是会员</h3>
                        <p>高清下载功能需要VIP会员权限</p>
                        <div class="permission-actions">
                            <button class="btn btn-primary" onclick="goToVIP()">购买VIP会员</button>
                            <button class="btn btn-secondary" onclick="buySingleIssue()">单独购买本期</button>
                        </div>
                        <div class="price-info">
                            <span>本期售价：¥15.00</span>
                        </div>
                    </div>
                `);
                return;
            }

            FashionApp.showModal('高清下载', `
                <div class="download-options">
                    <h3>选择下载方式</h3>
                    <div class="download-stats">
                        <div class="stat-item">
                            <span>今日可下载剩余次数：</span>
                            <span class="stat-value">${downloadRemaining}</span>
                        </div>
                        <div class="stat-item">
                            <span>赠送下载增量包剩余：</span>
                            <span class="stat-value">${bonusDownloads}</span>
                        </div>
                    </div>
                    <div class="download-methods">
                        <button class="btn btn-primary download-method" onclick="downloadToPhone()">
                            <i class="fas fa-mobile-alt"></i>
                            手机下载
                        </button>
                        <button class="btn btn-secondary download-method" onclick="downloadToPC()">
                            <i class="fas fa-desktop"></i>
                            电脑端下载
                        </button>
                    </div>
                </div>
            `);
        }

        function downloadToPhone() {
            if (downloadRemaining <= 0 && bonusDownloads <= 0) {
                FashionApp.showToast('下载次数已用完', 'error');
                return;
            }
            
            FashionApp.showToast('开始下载到手机...', 'success');
            closeModal();
        }

        function downloadToPC() {
            if (downloadRemaining <= 0 && bonusDownloads <= 0) {
                FashionApp.showToast('下载次数已用完', 'error');
                return;
            }

            FashionApp.showModal('电脑端下载', `
                <div class="pc-download">
                    <h3>电脑端下载</h3>
                    <div class="download-info">
                        <div class="info-item">
                            <span>下载网址：</span>
                            <span class="download-url">xz.123.com</span>
                        </div>
                        <div class="info-item">
                            <span>下载码：</span>
                            <span class="download-code">2233445</span>
                        </div>
                    </div>
                    <div class="download-note">
                        <p>* 下载码下载后失效，并记为一次下载</p>
                    </div>
                    <button class="btn btn-primary" onclick="confirmPCDownload()">确认下载</button>
                </div>
            `);
        }

        function confirmPCDownload() {
            FashionApp.showToast('下载码已生成，请在电脑端使用', 'success');
            closeModal();
        }

        function downloadImage(imageId) {
            if (isVIP) {
                FashionApp.showToast(`下载图片 ${imageId}`, 'success');
            } else {
                FashionApp.showToast('需要VIP权限才能下载', 'error');
            }
        }

        function shareImage(imageId) {
            FashionApp.showToast(`分享图片 ${imageId}`, 'info');
        }

        function shareMagazine() {
            closeModal();
            FashionApp.showToast('分享杂志', 'info');
        }

        function reportIssue() {
            closeModal();
            FashionApp.showToast('举报问题', 'info');
        }

        function addToFavorites() {
            closeModal();
            FashionApp.showToast('添加到收藏', 'success');
        }

        function goToVIP() {
            closeModal();
            FashionApp.showToast('跳转到VIP购买页面', 'info');
        }

        function buySingleIssue() {
            closeModal();
            FashionApp.showModal('购买确认', `
                <div class="purchase-confirm">
                    <h3>购买本期杂志</h3>
                    <div class="purchase-details">
                        <div class="detail-item">
                            <span>杂志：</span>
                            <span>第001期 · 春日物语</span>
                        </div>
                        <div class="detail-item">
                            <span>价格：</span>
                            <span>¥15.00</span>
                        </div>
                    </div>
                    <div class="confirm-actions">
                        <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                        <button class="btn btn-primary" onclick="confirmSinglePurchase()">确认购买</button>
                    </div>
                </div>
            `);
        }

        function confirmSinglePurchase() {
            FashionApp.showToast('购买成功！', 'success');
            closeModal();
        }

        function closeModal() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }
    </script>

    <style>
        .back-btn, .more-btn {
            background: none;
            border: none;
            color: var(--text-dark);
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .back-btn:hover, .more-btn:hover {
            background: var(--border-dark);
        }

        .magazine-cover-section {
            margin-bottom: 20px;
        }

        .cover-image {
            width: 100%;
            height: 200px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .cover-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .cover-info h2 {
            color: var(--text-dark);
            margin-bottom: 8px;
            font-size: 20px;
        }

        .cover-info p {
            color: var(--text-secondary-dark);
            font-size: 14px;
            margin: 0;
        }

        .content-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-dark);
            margin-bottom: 20px;
        }

        .tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 2px solid transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tab.active {
            color: var(--accent-dark);
            border-bottom-color: var(--accent-dark);
        }

        .tab:hover {
            background: var(--border-dark);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        .images-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }

        .image-item {
            position: relative;
            aspect-ratio: 3/4;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .image-item:hover {
            transform: scale(1.05);
        }

        .image-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .image-item:hover .image-overlay {
            opacity: 1;
        }

        .image-overlay i {
            color: white;
            font-size: 24px;
        }

        .videos-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .video-item {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 15px;
            border: 1px solid var(--border-dark);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .video-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-dark);
        }

        .video-thumbnail {
            position: relative;
            width: 100%;
            height: 120px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .play-button {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 50px;
            height: 50px;
            background: rgba(255, 107, 107, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }

        .video-duration {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }

        .video-info h4 {
            color: var(--text-dark);
            margin-bottom: 5px;
            font-size: 16px;
        }

        .video-info p {
            color: var(--text-secondary-dark);
            font-size: 14px;
            margin: 0;
        }

        .download-btn {
            position: fixed;
            right: 20px;
            bottom: 120px;
            background: var(--accent-dark);
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            box-shadow: var(--shadow-dark);
            transition: all 0.3s ease;
            z-index: 999;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 50px rgba(255, 107, 107, 0.4);
        }

        /* 模态框内容样式 */
        .more-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .option-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px;
            border-radius: 10px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .option-item:hover {
            background: var(--border-dark);
        }

        .option-item i {
            color: var(--accent-dark);
            width: 20px;
        }

        .image-preview {
            text-align: center;
        }

        .image-preview img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .image-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .vip-required,
        .download-permission {
            text-align: center;
        }

        .vip-icon,
        .permission-icon {
            font-size: 48px;
            color: #ffd700;
            margin-bottom: 20px;
        }

        .vip-required h3,
        .download-permission h3 {
            color: var(--text-dark);
            margin-bottom: 10px;
        }

        .vip-required p,
        .download-permission p {
            color: var(--text-secondary-dark);
            margin-bottom: 20px;
        }

        .vip-actions,
        .permission-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 15px;
        }

        .price-info {
            font-size: 14px;
            color: var(--text-secondary-dark);
        }

        .download-options {
            text-align: center;
        }

        .download-stats {
            margin: 20px 0;
            padding: 15px;
            background: var(--secondary-dark);
            border-radius: 10px;
            border: 1px solid var(--border-dark);
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .stat-item:last-child {
            margin-bottom: 0;
        }

        .stat-value {
            color: var(--accent-dark);
            font-weight: bold;
        }

        .download-methods {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .download-method {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .pc-download {
            text-align: center;
        }

        .download-info {
            margin: 20px 0;
            padding: 15px;
            background: var(--secondary-dark);
            border-radius: 10px;
            border: 1px solid var(--border-dark);
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .info-item:last-child {
            margin-bottom: 0;
        }

        .download-url,
        .download-code {
            color: var(--accent-dark);
            font-weight: bold;
            font-family: monospace;
        }

        .download-note {
            margin: 15px 0;
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .purchase-confirm {
            text-align: center;
        }

        .purchase-details {
            margin: 20px 0;
            padding: 15px;
            background: var(--secondary-dark);
            border-radius: 10px;
            border: 1px solid var(--border-dark);
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .detail-item:last-child {
            margin-bottom: 0;
        }

        .confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    </style>
</body>
</html>
