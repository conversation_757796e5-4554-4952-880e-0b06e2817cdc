<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - VIP会员</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info">VIP会员</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 当前会员状态 -->
            <div class="member-status">
                <div class="status-header">
                    <i class="fas fa-crown"></i>
                    <h2>会员状态</h2>
                </div>
                <div class="status-info">
                    <div class="status-item">
                        <span class="label">会员级别：</span>
                        <span class="value">普通用户</span>
                    </div>
                    <div class="status-item">
                        <span class="label">有效期：</span>
                        <span class="value">--</span>
                    </div>
                </div>
            </div>

            <!-- 会员体系按钮 -->
            <button class="btn btn-secondary member-system-btn" onclick="showMemberSystem()">
                <i class="fas fa-info-circle"></i>
                会员体系详情
            </button>

            <!-- VIP套餐选择 -->
            <div class="vip-packages">
                <h3 class="section-title">
                    <i class="fas fa-star"></i>
                    选择VIP套餐
                </h3>

                <!-- 超级VIP -->
                <div class="vip-package premium" onclick="selectPackage('super', 299)">
                    <div class="package-header">
                        <div class="package-icon">
                            <i class="fas fa-gem"></i>
                        </div>
                        <div class="package-info">
                            <h4>超级VIP会员</h4>
                            <p class="package-price">¥299/年</p>
                        </div>
                        <div class="package-badge">推荐</div>
                    </div>
                    <div class="package-features">
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>所有杂志无限阅读</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>高清图片下载</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>独家视频内容</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>专属客服支持</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>每月50次下载</span>
                        </div>
                    </div>
                </div>

                <!-- 高级VIP -->
                <div class="vip-package advanced" onclick="selectPackage('advanced', 199)">
                    <div class="package-header">
                        <div class="package-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="package-info">
                            <h4>高级VIP会员</h4>
                            <p class="package-price">¥199/年</p>
                        </div>
                    </div>
                    <div class="package-features">
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>所有杂志无限阅读</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>高清图片下载</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>每月20次下载</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-times"></i>
                            <span>独家视频内容</span>
                        </div>
                    </div>
                </div>

                <!-- 专属VIP -->
                <div class="vip-package exclusive" onclick="selectPackage('exclusive', 99)">
                    <div class="package-header">
                        <div class="package-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="package-info">
                            <h4>6个电子杂志专属VIP</h4>
                            <p class="package-price">¥99/年</p>
                        </div>
                    </div>
                    <div class="package-features">
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>指定6个杂志无限阅读</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-check"></i>
                            <span>每月10次下载</span>
                        </div>
                        <div class="feature">
                            <i class="fas fa-times"></i>
                            <span>高清图片下载</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部支付区域 -->
        <div class="payment-section">
            <div class="payment-info">
                <div class="balance-info">
                    <span>当前余额：</span>
                    <span class="balance-amount">¥156.00</span>
                </div>
                <div class="selected-package">
                    <span id="selected-package-name">请选择套餐</span>
                    <span id="selected-package-price">¥0</span>
                </div>
            </div>
            <button class="btn btn-primary payment-btn" id="payment-btn" onclick="handlePayment()" disabled>
                购买
            </button>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item active">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        let selectedPackage = null;
        let selectedPrice = 0;
        const currentBalance = 156.00;

        function selectPackage(packageType, price) {
            // 移除之前的选中状态
            document.querySelectorAll('.vip-package').forEach(pkg => {
                pkg.classList.remove('selected');
            });

            // 添加选中状态
            event.currentTarget.classList.add('selected');

            selectedPackage = packageType;
            selectedPrice = price;

            // 更新显示
            const packageNames = {
                'super': '超级VIP会员',
                'advanced': '高级VIP会员',
                'exclusive': '专属VIP会员'
            };

            document.getElementById('selected-package-name').textContent = packageNames[packageType];
            document.getElementById('selected-package-price').textContent = `¥${price}`;
            
            // 启用购买按钮
            const paymentBtn = document.getElementById('payment-btn');
            paymentBtn.disabled = false;
            paymentBtn.textContent = '购买';
        }

        function handlePayment() {
            if (!selectedPackage) {
                FashionApp.showToast('请先选择套餐', 'warning');
                return;
            }

            if (currentBalance < selectedPrice) {
                FashionApp.showModal('余额不足', `
                    <div class="insufficient-balance">
                        <p>您的余额不足，无法完成购买</p>
                        <div class="balance-details">
                            <div class="detail-item">
                                <span>当前余额：</span>
                                <span>¥${currentBalance.toFixed(2)}</span>
                            </div>
                            <div class="detail-item">
                                <span>需要金额：</span>
                                <span>¥${selectedPrice.toFixed(2)}</span>
                            </div>
                            <div class="detail-item shortage">
                                <span>还需充值：</span>
                                <span>¥${(selectedPrice - currentBalance).toFixed(2)}</span>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="goToRecharge()">去充值</button>
                    </div>
                `);
                return;
            }

            // 显示支付确认
            FashionApp.showModal('确认购买', `
                <div class="payment-confirmation">
                    <div class="confirm-details">
                        <div class="detail-item">
                            <span>套餐：</span>
                            <span>${document.getElementById('selected-package-name').textContent}</span>
                        </div>
                        <div class="detail-item">
                            <span>价格：</span>
                            <span>¥${selectedPrice.toFixed(2)}</span>
                        </div>
                        <div class="detail-item">
                            <span>支付后余额：</span>
                            <span>¥${(currentBalance - selectedPrice).toFixed(2)}</span>
                        </div>
                    </div>
                    <div class="confirm-actions">
                        <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                        <button class="btn btn-primary" onclick="confirmPayment()">确认支付</button>
                    </div>
                </div>
            `);
        }

        function confirmPayment() {
            // 模拟支付成功
            FashionApp.showToast('购买成功！', 'success');
            closeModal();
            
            // 更新会员状态
            setTimeout(() => {
                updateMemberStatus();
            }, 1000);
        }

        function updateMemberStatus() {
            const statusValue = document.querySelector('.status-info .value');
            const packageNames = {
                'super': '超级VIP会员',
                'advanced': '高级VIP会员',
                'exclusive': '专属VIP会员'
            };
            
            statusValue.textContent = packageNames[selectedPackage];
            
            // 更新有效期
            const expiryDate = new Date();
            expiryDate.setFullYear(expiryDate.getFullYear() + 1);
            document.querySelectorAll('.status-info .value')[1].textContent = expiryDate.toLocaleDateString('zh-CN');
        }

        function showMemberSystem() {
            FashionApp.showModal('会员体系详情', `
                <div class="member-system">
                    <div class="system-table">
                        <div class="table-header">
                            <div class="col">功能</div>
                            <div class="col">普通用户</div>
                            <div class="col">专属VIP</div>
                            <div class="col">高级VIP</div>
                            <div class="col">超级VIP</div>
                        </div>
                        <div class="table-row">
                            <div class="col">杂志阅读</div>
                            <div class="col">单次购买</div>
                            <div class="col">6本无限</div>
                            <div class="col">全部无限</div>
                            <div class="col">全部无限</div>
                        </div>
                        <div class="table-row">
                            <div class="col">高清下载</div>
                            <div class="col">❌</div>
                            <div class="col">❌</div>
                            <div class="col">✅</div>
                            <div class="col">✅</div>
                        </div>
                        <div class="table-row">
                            <div class="col">视频内容</div>
                            <div class="col">❌</div>
                            <div class="col">❌</div>
                            <div class="col">❌</div>
                            <div class="col">✅</div>
                        </div>
                        <div class="table-row">
                            <div class="col">下载次数</div>
                            <div class="col">0</div>
                            <div class="col">10/月</div>
                            <div class="col">20/月</div>
                            <div class="col">50/月</div>
                        </div>
                        <div class="table-row">
                            <div class="col">专属客服</div>
                            <div class="col">❌</div>
                            <div class="col">❌</div>
                            <div class="col">❌</div>
                            <div class="col">✅</div>
                        </div>
                        <div class="table-row">
                            <div class="col">年费价格</div>
                            <div class="col">免费</div>
                            <div class="col">¥99</div>
                            <div class="col">¥199</div>
                            <div class="col">¥299</div>
                        </div>
                    </div>
                </div>
            `);
        }

        function goToRecharge() {
            closeModal();
            FashionApp.showToast('跳转到充值页面', 'info');
        }

        function closeModal() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }
    </script>

    <style>
        .main-content {
            padding-bottom: 120px; /* 为底部支付区域留出空间 */
        }

        .member-status {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--border-dark);
        }

        .status-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .status-header i {
            color: #ffd700;
            font-size: 24px;
        }

        .status-header h2 {
            color: var(--text-dark);
            margin: 0;
        }

        .status-info {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
        }

        .status-item .label {
            color: var(--text-secondary-dark);
        }

        .status-item .value {
            color: var(--text-dark);
            font-weight: 600;
        }

        .member-system-btn {
            width: 100%;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .vip-packages {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .vip-package {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            border: 2px solid var(--border-dark);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .vip-package:hover {
            border-color: var(--accent-dark);
            transform: translateY(-2px);
        }

        .vip-package.selected {
            border-color: var(--accent-dark);
            box-shadow: var(--glow-dark);
        }

        .vip-package.premium {
            background: linear-gradient(135deg, var(--secondary-dark) 0%, rgba(255, 107, 107, 0.1) 100%);
        }

        .package-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            position: relative;
        }

        .package-icon {
            width: 50px;
            height: 50px;
            background: var(--accent-dark);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }

        .package-info h4 {
            margin: 0 0 5px 0;
            color: var(--text-dark);
        }

        .package-price {
            color: var(--accent-dark);
            font-weight: bold;
            font-size: 18px;
            margin: 0;
        }

        .package-badge {
            position: absolute;
            top: -10px;
            right: 0;
            background: var(--accent-dark);
            color: white;
            padding: 5px 10px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: bold;
        }

        .package-features {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .feature {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
        }

        .feature i.fa-check {
            color: #27ae60;
        }

        .feature i.fa-times {
            color: #e74c3c;
        }

        .payment-section {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 393px;
            background: var(--secondary-dark);
            border-top: 1px solid var(--border-dark);
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .payment-info {
            flex: 1;
        }

        .balance-info {
            font-size: 14px;
            color: var(--text-secondary-dark);
            margin-bottom: 5px;
        }

        .balance-amount {
            color: var(--accent-dark);
            font-weight: bold;
        }

        .selected-package {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .payment-btn {
            margin-left: 20px;
            padding: 12px 30px;
        }

        .payment-btn:disabled {
            background: var(--border-dark);
            color: var(--text-secondary-dark);
            cursor: not-allowed;
        }

        /* 模态框内容样式 */
        .insufficient-balance,
        .payment-confirmation {
            text-align: center;
        }

        .balance-details,
        .confirm-details {
            margin: 20px 0;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-dark);
        }

        .detail-item.shortage {
            color: var(--accent-dark);
            font-weight: bold;
        }

        .confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .system-table {
            font-size: 12px;
        }

        .table-header,
        .table-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            gap: 5px;
            padding: 8px 0;
            border-bottom: 1px solid var(--border-dark);
        }

        .table-header {
            font-weight: bold;
            background: var(--secondary-dark);
        }

        .col {
            text-align: center;
            padding: 5px;
        }
    </style>
</body>
</html>
