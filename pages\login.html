<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 登录</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info">登录</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="auth-container">
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="fas fa-camera-retro"></i>
                    </div>
                    <h2>欢迎回来</h2>
                    <p>登录您的账户，探索时尚摄影世界</p>
                </div>

                <form class="auth-form" onsubmit="handleLogin(event)">
                    <div class="form-group">
                        <label for="username">用户名</label>
                        <div class="input-group">
                            <i class="fas fa-user"></i>
                            <input type="text" id="username" class="form-input" placeholder="请输入用户名" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">密码</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" class="form-input" placeholder="请输入密码" required>
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="captcha">验证码</label>
                        <div class="captcha-group">
                            <div class="input-group">
                                <i class="fas fa-shield-alt"></i>
                                <input type="text" id="captcha" class="form-input" placeholder="请输入验证码" required>
                            </div>
                            <div class="captcha-image" onclick="refreshCaptcha()">
                                <canvas id="captchaCanvas" width="100" height="40"></canvas>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary auth-submit">
                        <i class="fas fa-sign-in-alt"></i>
                        登录
                    </button>
                </form>

                <div class="auth-links">
                    <a href="#" onclick="showRegisterPage()" class="auth-link">
                        <i class="fas fa-user-plus"></i>
                        注册新账户
                    </a>
                    <a href="#" onclick="showForgotPasswordPage()" class="auth-link">
                        <i class="fas fa-key"></i>
                        忘记密码？
                    </a>
                </div>

                <div class="auth-divider">
                    <span>或者</span>
                </div>

                <div class="social-login">
                    <button class="btn btn-secondary social-btn">
                        <i class="fab fa-wechat"></i>
                        微信登录
                    </button>
                    <button class="btn btn-secondary social-btn">
                        <i class="fab fa-qq"></i>
                        QQ登录
                    </button>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item active">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        // 生成验证码
        function generateCaptcha() {
            const canvas = document.getElementById('captchaCanvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 生成随机验证码
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let captcha = '';
            for (let i = 0; i < 4; i++) {
                captcha += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            
            // 设置样式
            ctx.fillStyle = '#ff6b6b';
            ctx.font = '20px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 绘制验证码
            ctx.fillText(captcha, canvas.width / 2, canvas.height / 2);
            
            // 添加干扰线
            for (let i = 0; i < 3; i++) {
                ctx.strokeStyle = '#cccccc';
                ctx.beginPath();
                ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
                ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
                ctx.stroke();
            }
            
            return captcha;
        }
        
        let currentCaptcha = generateCaptcha();
        
        function refreshCaptcha() {
            currentCaptcha = generateCaptcha();
        }
        
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleBtn = document.querySelector('.password-toggle i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }
        
        function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const captcha = document.getElementById('captcha').value;
            
            if (captcha.toUpperCase() !== currentCaptcha) {
                FashionApp.showToast('验证码错误', 'error');
                refreshCaptcha();
                return;
            }
            
            // 模拟登录
            FashionApp.showToast('登录成功！', 'success');
        }
        
        function showRegisterPage() {
            FashionApp.showToast('跳转到注册页面', 'info');
        }
        
        function showForgotPasswordPage() {
            FashionApp.showToast('跳转到密码找回页面', 'info');
        }
    </script>

    <style>
        .auth-container {
            max-width: 350px;
            margin: 0 auto;
            padding: 20px 0;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .auth-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: var(--accent-dark);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            box-shadow: var(--glow-dark);
        }
        
        .auth-header h2 {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--text-dark);
        }
        
        .auth-header p {
            color: var(--text-secondary-dark);
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .input-group {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .input-group i {
            position: absolute;
            left: 15px;
            color: var(--text-secondary-dark);
            z-index: 1;
        }
        
        .input-group .form-input {
            padding-left: 45px;
            margin-bottom: 0;
        }
        
        .password-toggle {
            position: absolute;
            right: 15px;
            background: none;
            border: none;
            color: var(--text-secondary-dark);
            cursor: pointer;
            z-index: 1;
        }
        
        .captcha-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }
        
        .captcha-group .input-group {
            flex: 1;
        }
        
        .captcha-image {
            background: var(--secondary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .captcha-image:hover {
            border-color: var(--accent-dark);
        }
        
        .auth-submit {
            width: 100%;
            margin: 30px 0 20px;
            padding: 15px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .auth-links {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .auth-link {
            color: var(--accent-dark);
            text-decoration: none;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: color 0.3s ease;
        }
        
        .auth-link:hover {
            color: #ff5252;
        }
        
        .auth-divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        
        .auth-divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--border-dark);
        }
        
        .auth-divider span {
            background: var(--primary-dark);
            padding: 0 15px;
            color: var(--text-secondary-dark);
            font-size: 14px;
        }
        
        .social-login {
            display: flex;
            gap: 10px;
        }
        
        .social-btn {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 12px;
            font-size: 14px;
        }
    </style>
</body>
</html>
