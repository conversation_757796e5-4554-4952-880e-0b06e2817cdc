<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 开屏</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 开屏内容 -->
        <div class="splash-container">
            <div class="splash-background">
                <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="时尚摄影背景">
                <div class="splash-overlay"></div>
            </div>
            
            <div class="splash-content">
                <div class="logo-section">
                    <div class="app-logo">
                        <i class="fas fa-camera-retro"></i>
                    </div>
                    <h1 class="app-title">时尚先锋摄影</h1>
                    <p class="app-subtitle">Fashion Pioneer Photography</p>
                </div>
                
                <div class="announcement-section">
                    <div class="announcement-card">
                        <div class="announcement-header">
                            <i class="fas fa-bullhorn"></i>
                            <span>最新公告</span>
                        </div>
                        <div class="announcement-content">
                            <h3>新春特惠活动开启</h3>
                            <p>全场VIP会员8折优惠，限时3天！更有精美礼品等你来拿。</p>
                            <div class="announcement-date">
                                <i class="fas fa-calendar-alt"></i>
                                <span>2024年1月15日</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="splash-actions">
                    <button class="btn btn-primary splash-enter" onclick="enterApp()">
                        <i class="fas fa-arrow-right"></i>
                        进入应用
                    </button>
                    <div class="splash-skip" onclick="enterApp()">
                        跳过 <span id="countdown">3</span>s
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        // 倒计时功能
        let countdown = 3;
        const countdownElement = document.getElementById('countdown');
        
        const timer = setInterval(() => {
            countdown--;
            countdownElement.textContent = countdown;
            
            if (countdown <= 0) {
                clearInterval(timer);
                enterApp();
            }
        }, 1000);
        
        // 进入应用
        function enterApp() {
            clearInterval(timer);
            // 这里可以跳转到首页或执行其他逻辑
            FashionApp.showToast('欢迎使用时尚先锋摄影！', 'success');
        }
    </script>

    <style>
        .splash-container {
            position: relative;
            height: calc(100% - 44px);
            overflow: hidden;
        }
        
        .splash-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        
        .splash-background img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .splash-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(26, 26, 26, 0.8), rgba(45, 45, 45, 0.6));
        }
        
        .splash-content {
            position: relative;
            z-index: 10;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            padding: 40px 20px 30px;
            text-align: center;
        }
        
        .logo-section {
            margin-top: 60px;
        }
        
        .app-logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 20px;
            background: var(--accent-dark);
            border-radius: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: white;
            box-shadow: var(--glow-dark);
        }
        
        .app-title {
            font-size: 32px;
            font-weight: bold;
            color: white;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        
        .app-subtitle {
            font-size: 16px;
            color: #cccccc;
            font-style: italic;
        }
        
        .announcement-section {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 0;
        }
        
        .announcement-card {
            background: rgba(45, 45, 45, 0.9);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid var(--border-dark);
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow-dark);
        }
        
        .announcement-header {
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--accent-dark);
            font-weight: bold;
            margin-bottom: 15px;
        }
        
        .announcement-content h3 {
            color: white;
            margin-bottom: 10px;
            font-size: 18px;
        }
        
        .announcement-content p {
            color: #cccccc;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .announcement-date {
            display: flex;
            align-items: center;
            gap: 5px;
            color: var(--text-secondary-dark);
            font-size: 14px;
        }
        
        .splash-actions {
            display: flex;
            flex-direction: column;
            gap: 15px;
            align-items: center;
        }
        
        .splash-enter {
            width: 200px;
            padding: 15px 30px;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .splash-skip {
            color: #cccccc;
            font-size: 14px;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        .splash-skip:hover {
            color: var(--accent-dark);
        }
    </style>
</body>
</html>
