/* 时尚先锋摄影 APP 样式文件 */

/* 全局变量 - 深色模式 */
:root {
  --primary-dark: #1a1a1a;
  --secondary-dark: #2d2d2d;
  --accent-dark: #ff6b6b;
  --text-dark: #ffffff;
  --text-secondary-dark: #cccccc;
  --border-dark: #404040;
  --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.3);
  --glow-dark: 0 0 20px rgba(255, 107, 107, 0.3);
}

/* 全局变量 - 浅色模式 */
[data-theme="light"] {
  --primary-dark: #ffffff;
  --secondary-dark: #f8f9fa;
  --accent-dark: #e74c3c;
  --text-dark: #2c3e50;
  --text-secondary-dark: #6c757d;
  --border-dark: #dee2e6;
  --gradient-dark: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.1);
  --glow-dark: 0 0 20px rgba(231, 76, 60, 0.2);
}

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--gradient-dark);
  color: var(--text-dark);
  transition: all 0.3s ease;
}

/* iPhone 15 Pro 尺寸容器 */
.phone-container {
  width: 393px;
  height: 852px;
  margin: 20px auto;
  background: var(--primary-dark);
  border-radius: 40px;
  overflow: hidden;
  box-shadow: var(--shadow-dark);
  position: relative;
  border: 2px solid var(--border-dark);
}

/* 状态栏 */
.status-bar {
  height: 44px;
  background: var(--primary-dark);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid var(--border-dark);
}

.status-left {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-right {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 顶部导航 */
.top-nav {
  height: 60px;
  background: var(--secondary-dark);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  border-bottom: 1px solid var(--border-dark);
}

.logo {
  font-size: 18px;
  font-weight: bold;
  color: var(--accent-dark);
}

.user-info {
  font-size: 14px;
  color: var(--text-secondary-dark);
  cursor: pointer;
  transition: color 0.3s ease;
}

.user-info:hover {
  color: var(--accent-dark);
}

/* 主要内容区域 */
.main-content {
  height: calc(852px - 44px - 60px - 80px);
  overflow-y: auto;
  padding: 20px;
}

/* 底部导航 */
.bottom-nav {
  height: 80px;
  background: var(--secondary-dark);
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid var(--border-dark);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 10px;
  border-radius: 10px;
}

.nav-item:hover {
  background: var(--accent-dark);
  transform: translateY(-2px);
}

.nav-item.active {
  color: var(--accent-dark);
}

/* 按钮样式 */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: var(--accent-dark);
  color: white;
  box-shadow: var(--glow-dark);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
  background: var(--secondary-dark);
  color: var(--text-dark);
  border: 1px solid var(--border-dark);
}

.btn-secondary:hover {
  background: var(--accent-dark);
  color: white;
}

/* 卡片样式 */
.card {
  background: var(--secondary-dark);
  border-radius: 15px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: var(--shadow-dark);
  border: 1px solid var(--border-dark);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.2);
}

/* 输入框样式 */
.form-input {
  width: 100%;
  padding: 15px;
  border: 1px solid var(--border-dark);
  border-radius: 10px;
  background: var(--primary-dark);
  color: var(--text-dark);
  font-size: 16px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--accent-dark);
  box-shadow: var(--glow-dark);
}

/* 主题切换按钮 */
.theme-toggle {
  position: fixed;
  bottom: 20px;
  left: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--accent-dark);
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  box-shadow: var(--shadow-dark);
  transition: all 0.3s ease;
  z-index: 1000;
}

.theme-toggle:hover {
  transform: scale(1.1);
}

/* 悬浮按钮 */
.floating-btn {
  position: fixed;
  right: 20px;
  bottom: 100px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--accent-dark);
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: var(--shadow-dark);
  transition: all 0.3s ease;
  z-index: 999;
}

.floating-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 15px 50px rgba(255, 107, 107, 0.4);
}

/* 响应式设计 */
@media (max-width: 480px) {
  .phone-container {
    width: 100%;
    height: 100vh;
    margin: 0;
    border-radius: 0;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--primary-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-dark);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #ff5252;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: var(--primary-dark);
  border-radius: 15px;
  max-width: 90%;
  max-height: 80%;
  overflow-y: auto;
  box-shadow: var(--shadow-dark);
  border: 1px solid var(--border-dark);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-dark);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-secondary-dark);
  cursor: pointer;
  transition: color 0.3s ease;
}

.modal-close:hover {
  color: var(--accent-dark);
}

.modal-body {
  padding: 20px;
}

/* 轮播图样式 */
.carousel {
  position: relative;
  overflow: hidden;
  border-radius: 15px;
  margin-bottom: 20px;
}

.carousel-container {
  display: flex;
  transition: transform 0.5s ease;
}

.carousel-slide {
  min-width: 100%;
  transition: transform 0.5s ease;
}

.carousel-slide img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 15px;
}

.carousel-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 15px;
}

.carousel-dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--border-dark);
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-dot.active {
  background: var(--accent-dark);
  transform: scale(1.2);
}

/* Toast 提示样式 */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 10px;
  color: white;
  font-weight: 600;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  z-index: 3000;
}

.toast.show {
  transform: translateX(0);
}

.toast-success {
  background: #27ae60;
}

.toast-error {
  background: #e74c3c;
}

.toast-warning {
  background: #f39c12;
}

.toast-info {
  background: var(--accent-dark);
}

/* 表单错误样式 */
.form-input.error {
  border-color: #e74c3c;
  box-shadow: 0 0 20px rgba(231, 76, 60, 0.3);
}

/* 网格布局 */
.grid {
  display: grid;
  gap: 20px;
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

/* 杂志封面样式 */
.magazine-cover {
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.magazine-cover:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-dark);
}

.magazine-cover img {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.magazine-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px 10px 10px;
  font-size: 12px;
  font-weight: 600;
}

/* 会员标签 */
.vip-badge {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #1a1a1a;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 12px;
  font-weight: bold;
  display: inline-block;
}

/* 价格标签 */
.price-tag {
  background: var(--accent-dark);
  color: white;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: bold;
  display: inline-block;
}

/* 搜索框样式 */
.search-container {
  position: relative;
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 15px 50px 15px 20px;
  border: 1px solid var(--border-dark);
  border-radius: 25px;
  background: var(--secondary-dark);
  color: var(--text-dark);
  font-size: 16px;
}

.search-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary-dark);
}

/* 标签页样式 */
.tabs {
  display: flex;
  border-bottom: 1px solid var(--border-dark);
  margin-bottom: 20px;
}

.tab {
  flex: 1;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 2px solid transparent;
}

.tab.active {
  color: var(--accent-dark);
  border-bottom-color: var(--accent-dark);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease;
}
