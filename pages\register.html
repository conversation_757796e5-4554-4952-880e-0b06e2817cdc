<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 注册</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info">注册</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="auth-container">
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <h2>创建账户</h2>
                    <p>加入时尚先锋摄影，开启您的时尚之旅</p>
                </div>

                <form class="auth-form" onsubmit="handleRegister(event)">
                    <div class="form-group">
                        <label for="reg-username">用户名</label>
                        <div class="input-group">
                            <i class="fas fa-user"></i>
                            <input type="text" id="reg-username" class="form-input" placeholder="请输入用户名" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="reg-password">密码</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="reg-password" class="form-input" placeholder="请输入密码" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('reg-password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="confirm-password">确认密码</label>
                        <div class="input-group">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="confirm-password" class="form-input" placeholder="请再次输入密码" required>
                            <button type="button" class="password-toggle" onclick="togglePassword('confirm-password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="security-question">密码提示问题</label>
                        <div class="input-group">
                            <i class="fas fa-question-circle"></i>
                            <select id="security-question" class="form-input" required>
                                <option value="">请选择密码提示问题</option>
                                <option value="pet">您的第一只宠物叫什么名字？</option>
                                <option value="school">您的小学校名是什么？</option>
                                <option value="city">您出生的城市是哪里？</option>
                                <option value="food">您最喜欢的食物是什么？</option>
                                <option value="book">您最喜欢的书籍是什么？</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="security-answer">密码提示答案</label>
                        <div class="input-group">
                            <i class="fas fa-key"></i>
                            <input type="text" id="security-answer" class="form-input" placeholder="请输入答案" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="checkbox-group">
                            <input type="checkbox" id="agree-terms" required>
                            <label for="agree-terms" class="checkbox-label">
                                我已阅读并同意
                                <a href="#" onclick="showTerms()" class="terms-link">《注册须知》</a>
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary auth-submit">
                        <i class="fas fa-user-plus"></i>
                        注册账户
                    </button>
                </form>

                <div class="auth-links">
                    <a href="#" onclick="showLoginPage()" class="auth-link">
                        <i class="fas fa-sign-in-alt"></i>
                        已有账户？立即登录
                    </a>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item active">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleBtn = passwordInput.parentElement.querySelector('.password-toggle i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }
        
        function handleRegister(event) {
            event.preventDefault();
            
            const username = document.getElementById('reg-username').value;
            const password = document.getElementById('reg-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            const securityQuestion = document.getElementById('security-question').value;
            const securityAnswer = document.getElementById('security-answer').value;
            const agreeTerms = document.getElementById('agree-terms').checked;
            
            // 验证密码一致性
            if (password !== confirmPassword) {
                FashionApp.showToast('两次输入的密码不一致', 'error');
                return;
            }
            
            // 验证密码强度
            if (password.length < 6) {
                FashionApp.showToast('密码长度至少6位', 'error');
                return;
            }
            
            // 验证是否同意条款
            if (!agreeTerms) {
                FashionApp.showToast('请先阅读并同意注册须知', 'error');
                return;
            }
            
            // 模拟注册
            FashionApp.showToast('注册成功！', 'success');
        }
        
        function showTerms() {
            FashionApp.showModal('注册须知', `
                <div class="terms-content">
                    <h3>用户注册协议</h3>
                    <div class="terms-text">
                        <p>1. 用户在注册时应提供真实、准确、完整的个人信息。</p>
                        <p>2. 用户应妥善保管账户信息，不得将账户借给他人使用。</p>
                        <p>3. 用户不得发布违法、违规、不当的内容。</p>
                        <p>4. 平台有权对违规用户进行处罚，包括但不限于警告、限制功能、封禁账户等。</p>
                        <p>5. 用户在使用平台服务时应遵守相关法律法规。</p>
                        <p>6. 平台保护用户隐私，不会泄露用户个人信息。</p>
                        <p>7. 本协议的解释权归时尚先锋摄影平台所有。</p>
                    </div>
                    <button class="btn btn-primary" onclick="agreeTerms()">我已阅读并同意</button>
                </div>
            `);
        }
        
        function agreeTerms() {
            document.getElementById('agree-terms').checked = true;
            // 关闭模态框
            document.querySelector('.modal-overlay').remove();
        }
        
        function showLoginPage() {
            FashionApp.showToast('跳转到登录页面', 'info');
        }
    </script>

    <style>
        .auth-container {
            max-width: 350px;
            margin: 0 auto;
            padding: 20px 0;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .auth-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: var(--accent-dark);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            box-shadow: var(--glow-dark);
        }
        
        .auth-header h2 {
            font-size: 24px;
            margin-bottom: 10px;
            color: var(--text-dark);
        }
        
        .auth-header p {
            color: var(--text-secondary-dark);
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .input-group {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .input-group i {
            position: absolute;
            left: 15px;
            color: var(--text-secondary-dark);
            z-index: 1;
        }
        
        .input-group .form-input {
            padding-left: 45px;
            margin-bottom: 0;
        }
        
        .input-group select.form-input {
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 12px center;
            background-repeat: no-repeat;
            background-size: 16px;
            padding-right: 40px;
        }
        
        .password-toggle {
            position: absolute;
            right: 15px;
            background: none;
            border: none;
            color: var(--text-secondary-dark);
            cursor: pointer;
            z-index: 1;
        }
        
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .checkbox-label {
            font-size: 14px;
            color: var(--text-secondary-dark);
            cursor: pointer;
        }
        
        .terms-link {
            color: var(--accent-dark);
            text-decoration: none;
        }
        
        .terms-link:hover {
            text-decoration: underline;
        }
        
        .auth-submit {
            width: 100%;
            margin: 30px 0 20px;
            padding: 15px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }
        
        .auth-links {
            text-align: center;
        }
        
        .auth-link {
            color: var(--accent-dark);
            text-decoration: none;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            transition: color 0.3s ease;
        }
        
        .auth-link:hover {
            color: #ff5252;
        }
        
        .terms-content {
            max-width: 400px;
        }
        
        .terms-text {
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
            padding: 15px;
            background: var(--secondary-dark);
            border-radius: 10px;
            border: 1px solid var(--border-dark);
        }
        
        .terms-text p {
            margin-bottom: 10px;
            line-height: 1.5;
            color: var(--text-secondary-dark);
        }
    </style>
</body>
</html>
