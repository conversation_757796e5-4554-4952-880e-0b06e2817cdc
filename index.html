<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 APP - 原型预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            padding: 20px;
            min-height: 100vh;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 32px;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .header p {
            font-size: 16px;
            color: #cccccc;
            margin-bottom: 20px;
        }

        .theme-controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .theme-btn {
            padding: 10px 20px;
            border: 2px solid #ff6b6b;
            background: transparent;
            color: #ff6b6b;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .theme-btn:hover,
        .theme-btn.active {
            background: #ff6b6b;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .page-container {
            background: #2d2d2d;
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.3);
            border: 1px solid #404040;
            transition: all 0.3s ease;
        }

        .page-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
        }

        .page-title {
            font-size: 18px;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 15px;
            text-align: center;
            padding: 10px;
            background: rgba(255, 107, 107, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .page-frame {
            width: 393px;
            height: 852px;
            margin: 0 auto;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            border: 2px solid #404040;
            background: #1a1a1a;
            position: relative;
        }

        .page-frame iframe {
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 38px;
        }

        .page-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: transparent;
            cursor: pointer;
            z-index: 10;
            border-radius: 38px;
        }

        .page-overlay:hover {
            background: rgba(255, 107, 107, 0.1);
        }

        .page-info {
            margin-top: 15px;
            text-align: center;
        }

        .page-description {
            font-size: 14px;
            color: #cccccc;
            line-height: 1.5;
            margin-bottom: 10px;
        }

        .page-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            justify-content: center;
        }

        .feature-tag {
            background: rgba(255, 107, 107, 0.2);
            color: #ff6b6b;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .controls {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }

        .control-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #ff6b6b;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);
        }

        .stats {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(45, 45, 45, 0.5);
            border-radius: 15px;
            border: 1px solid #404040;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b6b;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 14px;
            color: #cccccc;
        }

        @media (max-width: 768px) {
            .pages-grid {
                grid-template-columns: 1fr;
            }
            
            .page-frame {
                width: 100%;
                max-width: 393px;
                height: 600px;
            }
            
            .controls {
                position: relative;
                top: auto;
                right: auto;
                flex-direction: row;
                justify-content: center;
                margin-bottom: 20px;
            }
        }

        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #cccccc;
            font-size: 16px;
        }

        .loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid #ff6b6b;
            border-top: 2px solid transparent;
            border-radius: 50%;
            margin-left: 10px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>时尚先锋摄影 APP</h1>
        <p>Fashion Pioneer Photography - 高保真原型预览</p>
        
        <div class="stats">
            <h3>项目统计</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">16</div>
                    <div class="stat-label">页面总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">2</div>
                    <div class="stat-label">主题模式</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">移动适配</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">iPhone 15</div>
                    <div class="stat-label">设计标准</div>
                </div>
            </div>
        </div>

        <div class="theme-controls">
            <button class="theme-btn active" onclick="switchTheme('dark')">深色模式</button>
            <button class="theme-btn" onclick="switchTheme('light')">浅色模式</button>
        </div>
    </div>

    <div class="controls">
        <button class="control-btn" onclick="scrollToTop()" title="回到顶部">
            ↑
        </button>
        <button class="control-btn" onclick="toggleFullscreen()" title="全屏预览">
            ⛶
        </button>
    </div>

    <div class="pages-grid" id="pagesGrid">
        <!-- 页面将通过JavaScript动态加载 -->
    </div>

    <script>
        const pages = [
            {
                name: '开屏页面',
                file: 'pages/splash.html',
                description: '应用启动页面，包含品牌展示和最新公告',
                features: ['品牌展示', '公告通知', '自动跳转', '倒计时']
            },
            {
                name: '首页',
                file: 'pages/home.html',
                description: '主要功能入口，展示轮播图、功能按钮和电子杂志',
                features: ['轮播图', '功能导航', '杂志展示', '客服悬浮']
            },
            {
                name: '登录页面',
                file: 'pages/login.html',
                description: '用户登录界面，支持多种登录方式',
                features: ['表单验证', '验证码', '社交登录', '密码显隐']
            },
            {
                name: '注册页面',
                file: 'pages/register.html',
                description: '用户注册界面，包含完整的注册流程',
                features: ['信息填写', '安全问题', '协议确认', '表单验证']
            },
            {
                name: '密码找回',
                file: 'pages/forgot-password.html',
                description: '密码重置流程，通过安全问题验证身份',
                features: ['分步流程', '安全验证', '密码强度', '客服联系']
            },
            {
                name: 'VIP会员',
                file: 'pages/vip.html',
                description: 'VIP会员购买页面，展示不同会员套餐',
                features: ['套餐对比', '会员体系', '支付流程', '余额检查']
            },
            {
                name: '充值页面',
                file: 'pages/recharge.html',
                description: '账户充值功能，支持多种充值方式',
                features: ['在线充值', '充值卡', '快捷金额', '充值记录']
            },
            {
                name: '社区吐槽',
                file: 'pages/community.html',
                description: '社区帖子列表，用户可以浏览和搜索帖子',
                features: ['帖子列表', '搜索筛选', '分类标签', '统计信息']
            },
            {
                name: '帖子详情',
                file: 'pages/post-detail.html',
                description: '帖子详细内容页面，支持评论和互动',
                features: ['内容展示', '评论系统', '点赞收藏', '分享功能']
            },
            {
                name: '杂志详情',
                file: 'pages/magazine-detail.html',
                description: '电子杂志详情页面，展示杂志列表和购买选项',
                features: ['杂志列表', '搜索功能', 'VIP推广', '购买流程']
            },
            {
                name: '杂志阅读',
                file: 'pages/magazine-reader.html',
                description: '杂志阅读界面，支持图片和视频内容',
                features: ['标签切换', '图片预览', '视频播放', '下载功能']
            },
            {
                name: '搜索结果',
                file: 'pages/search-results.html',
                description: '搜索结果展示页面，支持多种内容类型',
                features: ['结果展示', '类型筛选', '排序选项', '高级筛选']
            },
            {
                name: '热榜页面',
                file: 'pages/rankings.html',
                description: '热门内容排行榜，支持多种排行类型',
                features: ['排行展示', '类型切换', '时间筛选', '趋势指示']
            },
            {
                name: '我的页面',
                file: 'pages/profile.html',
                description: '个人中心页面，包含用户信息和功能菜单',
                features: ['用户信息', '功能菜单', '消息通知', '设置选项']
            },
            {
                name: '模特页面',
                file: 'pages/models.html',
                description: '模特列表页面，展示所有模特信息',
                features: ['模特展示', '搜索筛选', '分类标签', '详细信息']
            },
            {
                name: '模特详情',
                file: 'pages/model-detail.html',
                description: '模特详细信息页面，展示作品和介绍',
                features: ['模特介绍', '作品展示', '关注功能', '私信功能']
            }
        ];

        let currentTheme = 'dark';

        function initializePages() {
            const grid = document.getElementById('pagesGrid');
            
            pages.forEach((page, index) => {
                const pageContainer = document.createElement('div');
                pageContainer.className = 'page-container';
                pageContainer.innerHTML = `
                    <div class="page-title">${page.name}</div>
                    <div class="page-frame">
                        <div class="loading">加载中...</div>
                        <div class="page-overlay" onclick="openFullscreen('${page.file}', '${page.name}')"></div>
                    </div>
                    <div class="page-info">
                        <div class="page-description">${page.description}</div>
                        <div class="page-features">
                            ${page.features.map(feature => `<span class="feature-tag">${feature}</span>`).join('')}
                        </div>
                    </div>
                `;
                
                grid.appendChild(pageContainer);
                
                // 延迟加载iframe以提高性能
                setTimeout(() => {
                    loadPageIframe(pageContainer, page.file);
                }, index * 200);
            });
        }

        function loadPageIframe(container, file) {
            const frame = container.querySelector('.page-frame');
            const loading = frame.querySelector('.loading');
            
            const iframe = document.createElement('iframe');
            iframe.src = file;
            iframe.onload = () => {
                loading.style.display = 'none';
                // 应用当前主题
                applyThemeToIframe(iframe);
            };
            
            frame.appendChild(iframe);
        }

        function applyThemeToIframe(iframe) {
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                const body = iframeDoc.body;
                if (body) {
                    body.setAttribute('data-theme', currentTheme);
                }
            } catch (e) {
                // 跨域限制，忽略错误
            }
        }

        function switchTheme(theme) {
            currentTheme = theme;
            
            // 更新按钮状态
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 应用主题到所有iframe
            document.querySelectorAll('iframe').forEach(iframe => {
                applyThemeToIframe(iframe);
            });
        }

        function openFullscreen(file, title) {
            const fullscreenWindow = window.open('', '_blank', 'width=393,height=852,scrollbars=no,resizable=yes');
            fullscreenWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>${title} - 时尚先锋摄影</title>
                    <style>
                        body { margin: 0; padding: 0; overflow: hidden; }
                        iframe { width: 100%; height: 100vh; border: none; }
                    </style>
                </head>
                <body>
                    <iframe src="${file}"></iframe>
                </body>
                </html>
            `);
        }

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 初始化页面
        document.addEventListener('DOMContentLoaded', initializePages);

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && document.fullscreenElement) {
                document.exitFullscreen();
            }
            if (e.key === 'F11') {
                e.preventDefault();
                toggleFullscreen();
            }
        });
    </script>
</body>
</html>
