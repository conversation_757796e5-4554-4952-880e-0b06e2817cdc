<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 电子杂志详情</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">
                <button class="back-btn" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                时尚周刊
            </div>
            <div class="user-info">
                <button class="search-btn" onclick="showSearch()">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- Banner区域 -->
            <div class="magazine-banner">
                <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="时尚周刊推荐">
                <div class="banner-overlay">
                    <div class="banner-content">
                        <h2>本期推荐</h2>
                        <p>春季时尚大片，展现自然之美</p>
                        <button class="btn btn-primary" onclick="readMagazine(2024001)">
                            立即阅读
                        </button>
                    </div>
                </div>
            </div>

            <!-- VIP广告条 -->
            <div class="vip-ad" id="vip-ad">
                <div class="ad-content">
                    <div class="ad-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="ad-text">
                        <h4>开通VIP会员</h4>
                        <p>畅享所有杂志，高清下载无限制</p>
                    </div>
                    <button class="btn btn-primary ad-btn" onclick="goToVIP()">
                        立即开通
                    </button>
                </div>
            </div>

            <!-- 购买规则广告条 -->
            <div class="purchase-rules">
                <div class="rules-content" onclick="showPurchaseRules()">
                    <i class="fas fa-info-circle"></i>
                    <span>查看本杂志购买价格及购买规则</span>
                    <i class="fas fa-chevron-right"></i>
                </div>
            </div>

            <!-- 搜索框 -->
            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索期数、模特名..." onkeyup="searchMagazines()">
                <i class="fas fa-search search-icon"></i>
            </div>

            <!-- 杂志列表 -->
            <div class="magazines-list">
                <div class="magazine-item" onclick="readMagazine(2024001)">
                    <div class="magazine-cover">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第001期">
                        <div class="issue-badge">最新</div>
                    </div>
                    <div class="magazine-info">
                        <h3 class="magazine-title">第001期 · 春日物语 · 模特：艾米丽</h3>
                        <p class="magazine-desc">春天的温柔与浪漫，在镜头下绽放</p>
                        <div class="magazine-meta">
                            <div class="magazine-actions">
                                <button class="action-btn collect-btn" onclick="toggleCollect(2024001, event)">
                                    <i class="fas fa-heart"></i>
                                    <span>156</span>
                                </button>
                                <button class="action-btn like-btn" onclick="toggleLike(2024001, event)">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span>89</span>
                                </button>
                            </div>
                            <button class="purchase-btn purchased" onclick="purchaseMagazine(2024001, event)">
                                已购买
                            </button>
                        </div>
                    </div>
                </div>

                <div class="magazine-item" onclick="readMagazine(2023012)">
                    <div class="magazine-cover">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第012期">
                    </div>
                    <div class="magazine-info">
                        <h3 class="magazine-title">第012期 · 都市夜色 · 模特：索菲亚</h3>
                        <p class="magazine-desc">霓虹灯下的时尚魅力，展现都市女性的独特韵味</p>
                        <div class="magazine-meta">
                            <div class="magazine-actions">
                                <button class="action-btn collect-btn" onclick="toggleCollect(2023012, event)">
                                    <i class="fas fa-heart"></i>
                                    <span>234</span>
                                </button>
                                <button class="action-btn like-btn" onclick="toggleLike(2023012, event)">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span>167</span>
                                </button>
                            </div>
                            <button class="purchase-btn" onclick="purchaseMagazine(2023012, event)">
                                ¥15.00
                            </button>
                        </div>
                    </div>
                </div>

                <div class="magazine-item" onclick="readMagazine(2023011)">
                    <div class="magazine-cover">
                        <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第011期">
                    </div>
                    <div class="magazine-info">
                        <h3 class="magazine-title">第011期 · 复古风情 · 模特：维多利亚</h3>
                        <p class="magazine-desc">重温经典，感受复古时尚的永恒魅力</p>
                        <div class="magazine-meta">
                            <div class="magazine-actions">
                                <button class="action-btn collect-btn" onclick="toggleCollect(2023011, event)">
                                    <i class="fas fa-heart"></i>
                                    <span>189</span>
                                </button>
                                <button class="action-btn like-btn" onclick="toggleLike(2023011, event)">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span>123</span>
                                </button>
                            </div>
                            <button class="purchase-btn" onclick="purchaseMagazine(2023011, event)">
                                ¥15.00
                            </button>
                        </div>
                    </div>
                </div>

                <div class="magazine-item" onclick="readMagazine(2023010)">
                    <div class="magazine-cover">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第010期">
                    </div>
                    <div class="magazine-info">
                        <h3 class="magazine-title">第010期 · 自然之美 · 模特：伊莎贝拉</h3>
                        <p class="magazine-desc">回归自然，探索原始的美丽与纯真</p>
                        <div class="magazine-meta">
                            <div class="magazine-actions">
                                <button class="action-btn collect-btn" onclick="toggleCollect(2023010, event)">
                                    <i class="fas fa-heart"></i>
                                    <span>145</span>
                                </button>
                                <button class="action-btn like-btn" onclick="toggleLike(2023010, event)">
                                    <i class="fas fa-thumbs-up"></i>
                                    <span>98</span>
                                </button>
                            </div>
                            <button class="purchase-btn" onclick="purchaseMagazine(2023010, event)">
                                ¥15.00
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">3</button>
                <button class="page-btn">...</button>
                <button class="page-btn">10</button>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <span>首页</span>
            </div>
            <div class="nav-item" onclick="FashionApp.goToModels()">
                <span>模特</span>
            </div>
            <div class="nav-item" onclick="FashionApp.goToRankings()">
                <span>热榜</span>
            </div>
            <div class="nav-item" onclick="FashionApp.goToProfile()">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        // 模拟用户VIP状态
        const isVIP = false;
        const userBalance = 156.00;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            if (isVIP) {
                document.getElementById('vip-ad').style.display = 'none';
            }
        });

        function goBack() {
            FashionApp.showToast('返回首页', 'info');
        }

        function showSearch() {
            const searchInput = document.querySelector('.search-input');
            searchInput.focus();
        }

        function searchMagazines() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            const magazines = document.querySelectorAll('.magazine-item');

            magazines.forEach(magazine => {
                const title = magazine.querySelector('.magazine-title').textContent.toLowerCase();
                const desc = magazine.querySelector('.magazine-desc').textContent.toLowerCase();

                if (title.includes(searchTerm) || desc.includes(searchTerm)) {
                    magazine.style.display = 'block';
                } else {
                    magazine.style.display = 'none';
                }
            });
        }

        function readMagazine(issueId) {
            FashionApp.showToast(`打开杂志: ${issueId}`, 'info');
            // 这里可以跳转到杂志阅读页面
        }

        function goToVIP() {
            FashionApp.showToast('跳转到VIP购买页面', 'info');
        }

        function showPurchaseRules() {
            FashionApp.showModal('购买规则', `
                <div class="purchase-rules-content">
                    <h3>时尚周刊购买规则</h3>
                    <div class="rules-list">
                        <div class="rule-item">
                            <h4>单期购买</h4>
                            <p>每期杂志单独购买价格为 ¥15.00</p>
                        </div>
                        <div class="rule-item">
                            <h4>VIP会员</h4>
                            <p>开通VIP会员后可免费阅读所有期刊</p>
                        </div>
                        <div class="rule-item">
                            <h4>下载权限</h4>
                            <p>购买后可在线阅读，VIP会员可下载高清版本</p>
                        </div>
                        <div class="rule-item">
                            <h4>有效期</h4>
                            <p>单期购买永久有效，VIP会员按订阅期限</p>
                        </div>
                    </div>
                </div>
            `);
        }

        function toggleCollect(issueId, event) {
            event.stopPropagation();
            const btn = event.currentTarget;
            const icon = btn.querySelector('i');

            if (btn.classList.contains('active')) {
                btn.classList.remove('active');
                icon.className = 'fas fa-heart';
                FashionApp.showToast('取消收藏', 'info');
            } else {
                btn.classList.add('active');
                icon.className = 'fas fa-heart';
                FashionApp.showToast('收藏成功', 'success');
            }
        }

        function toggleLike(issueId, event) {
            event.stopPropagation();
            const btn = event.currentTarget;
            const icon = btn.querySelector('i');
            const count = btn.querySelector('span');

            if (btn.classList.contains('active')) {
                btn.classList.remove('active');
                icon.className = 'fas fa-thumbs-up';
                count.textContent = parseInt(count.textContent) - 1;
                FashionApp.showToast('取消点赞', 'info');
            } else {
                btn.classList.add('active');
                icon.className = 'fas fa-thumbs-up';
                count.textContent = parseInt(count.textContent) + 1;
                FashionApp.showToast('点赞成功', 'success');
            }
        }

        function purchaseMagazine(issueId, event) {
            event.stopPropagation();

            const btn = event.currentTarget;
            if (btn.classList.contains('purchased')) {
                FashionApp.showToast('您已购买此期杂志', 'info');
                return;
            }

            const price = 15.00;

            if (userBalance < price) {
                FashionApp.showModal('余额不足', `
                    <div class="insufficient-balance">
                        <p>您的余额不足，无法购买此期杂志</p>
                        <div class="balance-info">
                            <div>当前余额：¥${userBalance.toFixed(2)}</div>
                            <div>需要金额：¥${price.toFixed(2)}</div>
                        </div>
                        <button class="btn btn-primary" onclick="goToRecharge()">去充值</button>
                    </div>
                `);
                return;
            }

            FashionApp.showModal('确认购买', `
                <div class="purchase-confirm">
                    <p>确认购买第${issueId}期杂志？</p>
                    <div class="purchase-info">
                        <div>价格：¥${price.toFixed(2)}</div>
                        <div>购买后余额：¥${(userBalance - price).toFixed(2)}</div>
                    </div>
                    <div class="confirm-actions">
                        <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                        <button class="btn btn-primary" onclick="confirmPurchase('${issueId}', ${price})">确认购买</button>
                    </div>
                </div>
            `);
        }

        function confirmPurchase(issueId, price) {
            FashionApp.showToast('购买成功！', 'success');
            closeModal();

            // 更新按钮状态
            const purchaseBtn = document.querySelector(`[onclick*="${issueId}"]`);
            if (purchaseBtn && purchaseBtn.classList.contains('purchase-btn')) {
                purchaseBtn.textContent = '已购买';
                purchaseBtn.classList.add('purchased');
            }
        }

        function goToRecharge() {
            closeModal();
            FashionApp.showToast('跳转到充值页面', 'info');
        }

        function closeModal() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }
    </script>

    <style>
        .back-btn, .search-btn {
            background: none;
            border: none;
            color: var(--text-dark);
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .back-btn:hover, .search-btn:hover {
            background: var(--border-dark);
        }

        .magazine-banner {
            position: relative;
            height: 200px;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .magazine-banner img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .banner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.3));
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .banner-content {
            text-align: center;
            color: white;
        }

        .banner-content h2 {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .banner-content p {
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .vip-ad {
            background: linear-gradient(135deg, var(--accent-dark), #ff5252);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 15px;
            color: white;
        }

        .ad-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .ad-icon {
            font-size: 24px;
        }

        .ad-text {
            flex: 1;
        }

        .ad-text h4 {
            margin: 0 0 5px 0;
            font-size: 16px;
        }

        .ad-text p {
            margin: 0;
            font-size: 12px;
            opacity: 0.9;
        }

        .ad-btn {
            padding: 8px 16px;
            font-size: 14px;
        }

        .purchase-rules {
            background: var(--secondary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .rules-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .rules-content:hover {
            background: var(--border-dark);
        }

        .rules-content i:first-child {
            color: var(--accent-dark);
        }

        .rules-content span {
            flex: 1;
            margin: 0 10px;
            color: var(--text-dark);
            font-size: 14px;
        }

        .magazines-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 30px;
        }

        .magazine-item {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 15px;
            border: 1px solid var(--border-dark);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .magazine-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-dark);
        }

        .magazine-cover {
            position: relative;
            width: 100%;
            height: 120px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .magazine-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .issue-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--accent-dark);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .magazine-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .magazine-desc {
            font-size: 14px;
            color: var(--text-secondary-dark);
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .magazine-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .magazine-actions {
            display: flex;
            gap: 15px;
        }

        .action-btn {
            background: none;
            border: none;
            color: var(--text-secondary-dark);
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: color 0.3s ease;
        }

        .action-btn:hover {
            color: var(--accent-dark);
        }

        .action-btn.active {
            color: var(--accent-dark);
        }

        .purchase-btn {
            background: var(--accent-dark);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .purchase-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--glow-dark);
        }

        .purchase-btn.purchased {
            background: var(--border-dark);
            color: var(--text-secondary-dark);
            cursor: default;
        }

        .purchase-btn.purchased:hover {
            transform: none;
            box-shadow: none;
        }

        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 20px;
        }

        .page-btn {
            width: 40px;
            height: 40px;
            border: 1px solid var(--border-dark);
            background: var(--secondary-dark);
            color: var(--text-dark);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .page-btn:hover {
            border-color: var(--accent-dark);
        }

        .page-btn.active {
            background: var(--accent-dark);
            color: white;
            border-color: var(--accent-dark);
        }

        /* 模态框内容样式 */
        .purchase-rules-content .rules-list {
            margin-top: 20px;
        }

        .rule-item {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-dark);
        }

        .rule-item:last-child {
            border-bottom: none;
        }

        .rule-item h4 {
            color: var(--accent-dark);
            margin-bottom: 8px;
        }

        .rule-item p {
            color: var(--text-secondary-dark);
            line-height: 1.5;
            margin: 0;
        }

        .insufficient-balance,
        .purchase-confirm {
            text-align: center;
        }

        .balance-info,
        .purchase-info {
            margin: 20px 0;
            padding: 15px;
            background: var(--secondary-dark);
            border-radius: 10px;
            border: 1px solid var(--border-dark);
        }

        .balance-info div,
        .purchase-info div {
            margin-bottom: 8px;
            color: var(--text-secondary-dark);
        }

        .confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    </style>
</body>
</html>
