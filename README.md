# 时尚先锋摄影 APP 原型

一个完整的时尚摄影电子杂志移动应用原型，采用现代化设计和交互体验。

## 📱 项目概述

时尚先锋摄影是一个专业的时尚摄影电子杂志平台，为用户提供高质量的时尚摄影内容、模特作品展示和社区交流功能。

### 🎯 核心功能

- **电子杂志阅读** - 高清时尚摄影杂志在线阅读
- **VIP会员体系** - 多层级会员服务和特权
- **模特展示** - 专业模特作品集和详细信息
- **社区互动** - 用户讨论、评论和分享功能
- **热榜排行** - 热门内容和趋势展示
- **搜索发现** - 智能搜索和内容推荐

## 🏗️ 项目结构

```
fashion-photography-app/
├── index.html              # 项目预览页面
├── README.md               # 项目说明文档
├── css/
│   └── styles.css          # 全局样式文件
├── js/
│   └── app.js              # 核心JavaScript功能
└── pages/
    ├── splash.html         # 开屏页面
    ├── home.html           # 首页
    ├── login.html          # 登录页面
    ├── register.html       # 注册页面
    ├── forgot-password.html # 密码找回
    ├── vip.html            # VIP会员
    ├── recharge.html       # 充值页面
    ├── community.html      # 社区吐槽
    ├── post-detail.html    # 帖子详情
    ├── magazine-detail.html # 杂志详情
    ├── magazine-reader.html # 杂志阅读
    ├── search-results.html # 搜索结果
    ├── rankings.html       # 热榜页面
    ├── profile.html        # 我的页面
    ├── models.html         # 模特页面
    └── model-detail.html   # 模特详情
```

## 🎨 设计特色

### 视觉设计
- **深色主题** - 现代化的深色界面设计
- **渐变效果** - 精美的渐变色彩搭配
- **圆角设计** - 友好的圆角界面元素
- **阴影效果** - 层次分明的阴影设计

### 交互体验
- **流畅动画** - 丰富的过渡动画效果
- **响应式布局** - 完美适配移动设备
- **手势支持** - 支持触摸手势操作
- **即时反馈** - 实时的用户操作反馈

### 技术特点
- **原生HTML/CSS/JS** - 无框架依赖，轻量高效
- **模块化设计** - 清晰的代码结构和组件划分
- **主题切换** - 支持深色/浅色主题切换
- **移动优先** - 专为移动设备优化设计

## 📋 页面功能详情

### 🚀 开屏页面 (splash.html)
- 品牌Logo展示
- 最新公告通知
- 自动跳转倒计时
- 主题切换功能

### 🏠 首页 (home.html)
- 轮播图展示
- 功能快捷入口
- 电子杂志网格
- 客服悬浮按钮

### 🔐 用户系统
- **登录页面** - 用户名/密码登录，验证码，社交登录
- **注册页面** - 完整注册流程，安全问题设置
- **密码找回** - 分步骤密码重置流程

### 💎 VIP会员 (vip.html)
- 会员等级展示
- 套餐对比表格
- 购买流程设计
- 余额检查机制

### 💰 充值系统 (recharge.html)
- 在线支付入口
- 充值卡充值
- 快捷金额选择
- 充值记录查询

### 💬 社区功能
- **社区列表** - 帖子展示，搜索筛选
- **帖子详情** - 内容展示，评论互动

### 📖 杂志系统
- **杂志详情** - 期刊列表，购买选项
- **杂志阅读** - 图片/视频内容，下载功能

### 🔍 搜索发现
- **搜索结果** - 多类型内容展示
- **热榜排行** - 排行榜展示，趋势分析

### 👤 个人中心
- **我的页面** - 用户信息，功能菜单
- **模特页面** - 模特列表，筛选搜索
- **模特详情** - 作品展示，关注互动

## 🚀 快速开始

### 本地预览

1. 克隆或下载项目文件
2. 使用本地服务器打开项目（推荐使用Live Server）
3. 访问 `index.html` 查看完整原型预览

### 在线预览

直接在浏览器中打开 `index.html` 文件即可预览所有页面。

### 单页面预览

可以直接访问 `pages/` 目录下的任意HTML文件查看单个页面。

## 🎯 使用说明

### 主题切换
- 点击右上角的主题切换按钮可以在深色/浅色主题间切换
- 所有页面都支持主题切换功能

### 交互功能
- 所有按钮和链接都有点击反馈
- 表单包含完整的验证逻辑
- 模态框和弹窗提供丰富的交互体验

### 响应式设计
- 所有页面都针对移动设备优化
- 支持不同屏幕尺寸的自适应布局

## 🛠️ 技术栈

- **HTML5** - 语义化标记
- **CSS3** - 现代样式特性
- **JavaScript ES6+** - 原生JavaScript
- **Font Awesome** - 图标库
- **Unsplash** - 高质量图片素材

## 📱 设备兼容性

- **移动设备** - iOS Safari, Android Chrome
- **桌面浏览器** - Chrome, Firefox, Safari, Edge
- **屏幕尺寸** - 320px - 1920px+

## 🎨 设计规范

### 颜色方案
- **主色调** - #ff6b6b (珊瑚红)
- **背景色** - #1a1a1a (深灰)
- **次要背景** - #2d2d2d (中灰)
- **文字颜色** - #ffffff / #e0e0e0

### 字体规范
- **主字体** - -apple-system, BlinkMacSystemFont, 'Segoe UI'
- **字体大小** - 12px - 32px
- **行高** - 1.4 - 1.6

### 间距规范
- **基础间距** - 8px的倍数
- **组件间距** - 15px - 30px
- **页面边距** - 20px

## 📄 许可证

本项目仅用于学习和展示目的。

## 🤝 贡献

欢迎提出建议和改进意见！

---

**时尚先锋摄影 APP** - 让时尚触手可及 ✨
