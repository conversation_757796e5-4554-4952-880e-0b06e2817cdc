<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 热榜</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info">热榜</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 排行榜类型选择 -->
            <div class="ranking-types">
                <div class="ranking-type active" onclick="switchRankingType('total')">
                    <i class="fas fa-trophy"></i>
                    <span>总榜</span>
                </div>
                <div class="ranking-type" onclick="switchRankingType('fashion-weekly')">
                    <i class="fas fa-star"></i>
                    <span>时尚周刊</span>
                </div>
                <div class="ranking-type" onclick="switchRankingType('beauty-world')">
                    <i class="fas fa-heart"></i>
                    <span>美丽世界</span>
                </div>
                <div class="ranking-type" onclick="switchRankingType('model-life')">
                    <i class="fas fa-user"></i>
                    <span>模特生活</span>
                </div>
                <div class="ranking-type" onclick="switchRankingType('photo-art')">
                    <i class="fas fa-camera"></i>
                    <span>摄影艺术</span>
                </div>
                <div class="ranking-type" onclick="switchRankingType('style-guide')">
                    <i class="fas fa-palette"></i>
                    <span>风格指南</span>
                </div>
                <div class="ranking-type" onclick="switchRankingType('luxury-fashion')">
                    <i class="fas fa-gem"></i>
                    <span>奢华时尚</span>
                </div>
            </div>

            <!-- 时间周期选择 -->
            <div class="time-periods">
                <div class="time-period active" onclick="switchTimePeriod('total')">总榜</div>
                <div class="time-period" onclick="switchTimePeriod('month')">月榜</div>
                <div class="time-period" onclick="switchTimePeriod('quarter')">季度榜</div>
            </div>

            <!-- 排行榜内容 -->
            <div class="ranking-content">
                <!-- 总榜 -->
                <div class="ranking-list active" id="total-ranking">
                    <div class="ranking-item top-1" onclick="openMagazine('2024001')">
                        <div class="ranking-number">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第001期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第001期 · 春日物语</h3>
                            <p class="ranking-magazine">时尚周刊</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 12.5k</span>
                                <span><i class="fas fa-heart"></i> 2.3k</span>
                                <span><i class="fas fa-download"></i> 890</span>
                            </div>
                        </div>
                        <div class="ranking-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+3</span>
                        </div>
                    </div>

                    <div class="ranking-item top-2" onclick="openMagazine('2023012')">
                        <div class="ranking-number">
                            <span>2</span>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第012期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第012期 · 都市夜色</h3>
                            <p class="ranking-magazine">美丽世界</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 10.2k</span>
                                <span><i class="fas fa-heart"></i> 1.8k</span>
                                <span><i class="fas fa-download"></i> 756</span>
                            </div>
                        </div>
                        <div class="ranking-trend down">
                            <i class="fas fa-arrow-down"></i>
                            <span>-1</span>
                        </div>
                    </div>

                    <div class="ranking-item top-3" onclick="openMagazine('2023011')">
                        <div class="ranking-number">
                            <span>3</span>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第011期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第011期 · 复古风情</h3>
                            <p class="ranking-magazine">风格指南</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 9.8k</span>
                                <span><i class="fas fa-heart"></i> 1.6k</span>
                                <span><i class="fas fa-download"></i> 623</span>
                            </div>
                        </div>
                        <div class="ranking-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+2</span>
                        </div>
                    </div>

                    <div class="ranking-item" onclick="openMagazine('2023010')">
                        <div class="ranking-number">
                            <span>4</span>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第010期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第010期 · 自然之美</h3>
                            <p class="ranking-magazine">摄影艺术</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 8.9k</span>
                                <span><i class="fas fa-heart"></i> 1.4k</span>
                                <span><i class="fas fa-download"></i> 567</span>
                            </div>
                        </div>
                        <div class="ranking-trend same">
                            <i class="fas fa-minus"></i>
                            <span>0</span>
                        </div>
                    </div>

                    <div class="ranking-item" onclick="openMagazine('2023009')">
                        <div class="ranking-number">
                            <span>5</span>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第009期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第009期 · 时尚前沿</h3>
                            <p class="ranking-magazine">奢华时尚</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 7.6k</span>
                                <span><i class="fas fa-heart"></i> 1.2k</span>
                                <span><i class="fas fa-download"></i> 445</span>
                            </div>
                        </div>
                        <div class="ranking-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+1</span>
                        </div>
                    </div>

                    <div class="ranking-item" onclick="openMagazine('2023008')">
                        <div class="ranking-number">
                            <span>6</span>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第008期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第008期 · 青春活力</h3>
                            <p class="ranking-magazine">模特生活</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 6.8k</span>
                                <span><i class="fas fa-heart"></i> 1.1k</span>
                                <span><i class="fas fa-download"></i> 389</span>
                            </div>
                        </div>
                        <div class="ranking-trend down">
                            <i class="fas fa-arrow-down"></i>
                            <span>-2</span>
                        </div>
                    </div>

                    <div class="ranking-item" onclick="openMagazine('2023007')">
                        <div class="ranking-number">
                            <span>7</span>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第007期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第007期 · 经典重现</h3>
                            <p class="ranking-magazine">时尚周刊</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 6.2k</span>
                                <span><i class="fas fa-heart"></i> 980</span>
                                <span><i class="fas fa-download"></i> 334</span>
                            </div>
                        </div>
                        <div class="ranking-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+4</span>
                        </div>
                    </div>

                    <div class="ranking-item" onclick="openMagazine('2023006')">
                        <div class="ranking-number">
                            <span>8</span>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第006期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第006期 · 梦幻色彩</h3>
                            <p class="ranking-magazine">美丽世界</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 5.9k</span>
                                <span><i class="fas fa-heart"></i> 876</span>
                                <span><i class="fas fa-download"></i> 298</span>
                            </div>
                        </div>
                        <div class="ranking-trend same">
                            <i class="fas fa-minus"></i>
                            <span>0</span>
                        </div>
                    </div>

                    <div class="ranking-item" onclick="openMagazine('2023005')">
                        <div class="ranking-number">
                            <span>9</span>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第005期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第005期 · 简约之美</h3>
                            <p class="ranking-magazine">风格指南</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 5.4k</span>
                                <span><i class="fas fa-heart"></i> 743</span>
                                <span><i class="fas fa-download"></i> 267</span>
                            </div>
                        </div>
                        <div class="ranking-trend down">
                            <i class="fas fa-arrow-down"></i>
                            <span>-1</span>
                        </div>
                    </div>

                    <div class="ranking-item" onclick="openMagazine('2023004')">
                        <div class="ranking-number">
                            <span>10</span>
                        </div>
                        <div class="ranking-cover">
                            <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="第004期">
                        </div>
                        <div class="ranking-info">
                            <h3 class="ranking-title">第004期 · 光影艺术</h3>
                            <p class="ranking-magazine">摄影艺术</p>
                            <div class="ranking-stats">
                                <span><i class="fas fa-eye"></i> 5.1k</span>
                                <span><i class="fas fa-heart"></i> 689</span>
                                <span><i class="fas fa-download"></i> 234</span>
                            </div>
                        </div>
                        <div class="ranking-trend up">
                            <i class="fas fa-arrow-up"></i>
                            <span>+3</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 更新时间 -->
            <div class="update-time">
                <i class="fas fa-clock"></i>
                <span>更新时间：2024年1月15日 18:00</span>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item active">
                <span>热榜</span>
            </div>
            <div class="nav-item">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        function switchRankingType(type) {
            // 更新排行榜类型选择状态
            document.querySelectorAll('.ranking-type').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            FashionApp.showToast(`切换到${event.currentTarget.querySelector('span').textContent}排行榜`, 'info');
        }

        function switchTimePeriod(period) {
            // 更新时间周期选择状态
            document.querySelectorAll('.time-period').forEach(item => {
                item.classList.remove('active');
            });
            event.currentTarget.classList.add('active');

            const periodNames = {
                'total': '总榜',
                'month': '月榜',
                'quarter': '季度榜'
            };

            FashionApp.showToast(`切换到${periodNames[period]}`, 'info');
        }

        function openMagazine(magazineId) {
            FashionApp.showToast(`打开杂志: ${magazineId}`, 'info');
        }
    </script>

    <style>
        .ranking-types {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto;
            padding-bottom: 5px;
        }

        .ranking-type {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            padding: 12px 16px;
            background: var(--secondary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 70px;
            white-space: nowrap;
        }

        .ranking-type.active {
            background: var(--accent-dark);
            color: white;
            border-color: var(--accent-dark);
        }

        .ranking-type:hover {
            border-color: var(--accent-dark);
        }

        .ranking-type i {
            font-size: 18px;
        }

        .ranking-type span {
            font-size: 12px;
            font-weight: 600;
        }

        .time-periods {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .time-period {
            flex: 1;
            padding: 12px;
            text-align: center;
            background: var(--secondary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .time-period.active {
            background: var(--accent-dark);
            color: white;
            border-color: var(--accent-dark);
        }

        .time-period:hover {
            border-color: var(--accent-dark);
        }

        .ranking-content {
            margin-bottom: 20px;
        }

        .ranking-list {
            display: none;
        }

        .ranking-list.active {
            display: block;
        }

        .ranking-item {
            display: flex;
            align-items: center;
            gap: 15px;
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid var(--border-dark);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ranking-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-dark);
        }

        .ranking-item.top-1 {
            background: linear-gradient(135deg, var(--secondary-dark) 0%, rgba(255, 215, 0, 0.1) 100%);
            border-color: #ffd700;
        }

        .ranking-item.top-2 {
            background: linear-gradient(135deg, var(--secondary-dark) 0%, rgba(192, 192, 192, 0.1) 100%);
            border-color: #c0c0c0;
        }

        .ranking-item.top-3 {
            background: linear-gradient(135deg, var(--secondary-dark) 0%, rgba(205, 127, 50, 0.1) 100%);
            border-color: #cd7f32;
        }

        .ranking-number {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .ranking-item.top-1 .ranking-number {
            background: #ffd700;
            color: #1a1a1a;
        }

        .ranking-item.top-2 .ranking-number {
            background: #c0c0c0;
            color: #1a1a1a;
        }

        .ranking-item.top-3 .ranking-number {
            background: #cd7f32;
            color: white;
        }

        .ranking-number span {
            color: var(--text-dark);
        }

        .ranking-cover {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .ranking-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .ranking-info {
            flex: 1;
        }

        .ranking-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 5px;
            line-height: 1.3;
        }

        .ranking-magazine {
            font-size: 12px;
            color: var(--text-secondary-dark);
            margin-bottom: 8px;
        }

        .ranking-stats {
            display: flex;
            gap: 12px;
        }

        .ranking-stats span {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            color: var(--text-secondary-dark);
        }

        .ranking-stats i {
            font-size: 11px;
        }

        .ranking-trend {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
            font-size: 12px;
            font-weight: bold;
            flex-shrink: 0;
        }

        .ranking-trend.up {
            color: #27ae60;
        }

        .ranking-trend.down {
            color: #e74c3c;
        }

        .ranking-trend.same {
            color: var(--text-secondary-dark);
        }

        .ranking-trend i {
            font-size: 14px;
        }

        .update-time {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: var(--text-secondary-dark);
            font-size: 12px;
            padding: 15px;
            background: var(--secondary-dark);
            border-radius: 10px;
            border: 1px solid var(--border-dark);
        }

        .update-time i {
            color: var(--accent-dark);
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .ranking-stats {
                gap: 8px;
            }
            
            .ranking-stats span {
                font-size: 10px;
            }
            
            .ranking-title {
                font-size: 14px;
            }
        }
    </style>
</body>
</html>
