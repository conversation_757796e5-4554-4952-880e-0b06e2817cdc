<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 帖子详情</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">
                <button class="back-btn" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                帖子详情
            </div>
            <div class="user-info">
                <button class="share-btn" onclick="sharePost()">
                    <i class="fas fa-share-alt"></i>
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 帖子内容 -->
            <div class="post-detail">
                <div class="post-header">
                    <h1 class="post-title">2024年春季时尚趋势大讨论</h1>
                    <div class="post-info">
                        <div class="author-info">
                            <div class="author-avatar">
                                <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="作者头像">
                            </div>
                            <div class="author-details">
                                <div class="author-name">时尚达人小美</div>
                                <div class="post-date">2024年1月15日 14:30</div>
                            </div>
                        </div>
                        <div class="post-stats">
                            <span><i class="fas fa-eye"></i> 1.2k</span>
                            <span><i class="fas fa-heart"></i> 156</span>
                        </div>
                    </div>
                </div>

                <div class="post-content">
                    <div class="content-image">
                        <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="时尚趋势">
                    </div>
                    
                    <div class="content-text">
                        <p>今年春季的时尚趋势真的让人眼前一亮！从复古回潮到未来主义，各种风格百花齐放，让我们一起来看看今年春季最值得关注的几个时尚趋势。</p>
                        
                        <h3>1. 复古回潮</h3>
                        <p>70年代和80年代的复古风格强势回归，宽肩膀、高腰裤、复古印花等元素重新成为时尚焦点。这种复古风不仅体现在服装上，配饰和妆容也都带有浓浓的复古味道。</p>
                        
                        <h3>2. 可持续时尚</h3>
                        <p>环保意识的提升让可持续时尚成为主流。越来越多的品牌开始使用环保材料，推出可循环利用的服装系列。这不仅是一种时尚态度，更是对地球的责任。</p>
                        
                        <h3>3. 性别模糊</h3>
                        <p>性别界限在时尚界越来越模糊，中性风格的服装受到更多人的喜爱。无论男女，都可以自由选择适合自己的风格，不再受传统性别观念的束缚。</p>
                        
                        <p>你们觉得今年春季哪个趋势最吸引人呢？欢迎在评论区分享你们的看法！</p>
                    </div>
                </div>

                <div class="post-actions">
                    <button class="action-btn like-btn" onclick="toggleLike()">
                        <i class="fas fa-heart"></i>
                        <span>点赞 (156)</span>
                    </button>
                    <button class="action-btn collect-btn" onclick="toggleCollect()">
                        <i class="fas fa-bookmark"></i>
                        <span>收藏</span>
                    </button>
                    <button class="action-btn comment-btn" onclick="focusComment()">
                        <i class="fas fa-comment"></i>
                        <span>评论 (89)</span>
                    </button>
                </div>
            </div>

            <!-- 评论区 -->
            <div class="comments-section">
                <div class="comments-header">
                    <h3>评论 (89)</h3>
                    <div class="sort-options">
                        <select class="sort-select" onchange="sortComments()">
                            <option value="time">按时间排序</option>
                            <option value="hot">按热度排序</option>
                        </select>
                    </div>
                </div>

                <div class="comments-list">
                    <div class="comment-item">
                        <div class="comment-avatar">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="用户头像">
                        </div>
                        <div class="comment-content">
                            <div class="comment-header">
                                <span class="comment-author">摄影师小王</span>
                                <span class="comment-time">2小时前</span>
                            </div>
                            <div class="comment-text">
                                说得太对了！今年的复古风真的很有意思，我最近拍的几组照片都融入了70年代的元素，效果特别棒。
                            </div>
                            <div class="comment-actions">
                                <button class="comment-like" onclick="likeComment(1)">
                                    <i class="fas fa-heart"></i> 12
                                </button>
                                <button class="comment-reply" onclick="replyComment(1)">
                                    <i class="fas fa-reply"></i> 回复
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="comment-item">
                        <div class="comment-avatar">
                            <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="用户头像">
                        </div>
                        <div class="comment-content">
                            <div class="comment-header">
                                <span class="comment-author">时尚博主Lisa</span>
                                <span class="comment-time">3小时前</span>
                            </div>
                            <div class="comment-text">
                                可持续时尚真的很重要！我现在买衣服都会考虑品牌的环保理念，希望更多人能关注这个话题。
                            </div>
                            <div class="comment-actions">
                                <button class="comment-like" onclick="likeComment(2)">
                                    <i class="fas fa-heart"></i> 8
                                </button>
                                <button class="comment-reply" onclick="replyComment(2)">
                                    <i class="fas fa-reply"></i> 回复
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="comment-item">
                        <div class="comment-avatar">
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="用户头像">
                        </div>
                        <div class="comment-content">
                            <div class="comment-header">
                                <span class="comment-author">设计师Alex</span>
                                <span class="comment-time">5小时前</span>
                            </div>
                            <div class="comment-text">
                                性别模糊的趋势我很赞同，时尚本来就应该是自由的表达，不应该被性别限制。
                            </div>
                            <div class="comment-actions">
                                <button class="comment-like" onclick="likeComment(3)">
                                    <i class="fas fa-heart"></i> 15
                                </button>
                                <button class="comment-reply" onclick="replyComment(3)">
                                    <i class="fas fa-reply"></i> 回复
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="load-more-comments">
                    <button class="btn btn-secondary" onclick="loadMoreComments()">
                        查看更多评论
                    </button>
                </div>
            </div>
        </div>

        <!-- 评论输入框 -->
        <div class="comment-input-section">
            <div class="comment-input-container">
                <input type="text" id="comment-input" class="comment-input" placeholder="写下你的评论..." disabled>
                <button class="send-btn" onclick="sendComment()" disabled>
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
            <div class="login-prompt">
                <span>请先 <a href="#" onclick="showLogin()">登录</a> 后再发表评论</span>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item active">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        let isLiked = false;
        let isCollected = false;

        function goBack() {
            FashionApp.showToast('返回上一页', 'info');
        }

        function sharePost() {
            FashionApp.showModal('分享帖子', `
                <div class="share-options">
                    <div class="share-item" onclick="shareToWeChat()">
                        <i class="fab fa-wechat"></i>
                        <span>微信</span>
                    </div>
                    <div class="share-item" onclick="shareToWeibo()">
                        <i class="fab fa-weibo"></i>
                        <span>微博</span>
                    </div>
                    <div class="share-item" onclick="copyLink()">
                        <i class="fas fa-link"></i>
                        <span>复制链接</span>
                    </div>
                </div>
            `);
        }

        function toggleLike() {
            const likeBtn = document.querySelector('.like-btn');
            const likeCount = likeBtn.querySelector('span');
            
            if (isLiked) {
                likeBtn.classList.remove('active');
                likeCount.textContent = '点赞 (155)';
                FashionApp.showToast('取消点赞', 'info');
            } else {
                likeBtn.classList.add('active');
                likeCount.textContent = '点赞 (157)';
                FashionApp.showToast('点赞成功', 'success');
            }
            isLiked = !isLiked;
        }

        function toggleCollect() {
            const collectBtn = document.querySelector('.collect-btn');
            
            if (isCollected) {
                collectBtn.classList.remove('active');
                FashionApp.showToast('取消收藏', 'info');
            } else {
                collectBtn.classList.add('active');
                FashionApp.showToast('收藏成功', 'success');
            }
            isCollected = !isCollected;
        }

        function focusComment() {
            document.getElementById('comment-input').focus();
        }

        function sortComments() {
            const sortValue = document.querySelector('.sort-select').value;
            FashionApp.showToast(`按${sortValue === 'time' ? '时间' : '热度'}排序`, 'info');
        }

        function likeComment(commentId) {
            FashionApp.showToast(`点赞评论 ${commentId}`, 'success');
        }

        function replyComment(commentId) {
            const commentInput = document.getElementById('comment-input');
            commentInput.placeholder = `回复评论 ${commentId}...`;
            commentInput.focus();
        }

        function loadMoreComments() {
            FashionApp.showToast('加载更多评论...', 'info');
        }

        function sendComment() {
            const commentText = document.getElementById('comment-input').value;
            if (commentText.trim()) {
                FashionApp.showToast('评论发表成功', 'success');
                document.getElementById('comment-input').value = '';
            }
        }

        function showLogin() {
            FashionApp.showToast('跳转到登录页面', 'info');
        }

        function shareToWeChat() {
            FashionApp.showToast('分享到微信', 'success');
        }

        function shareToWeibo() {
            FashionApp.showToast('分享到微博', 'success');
        }

        function copyLink() {
            FashionApp.showToast('链接已复制到剪贴板', 'success');
        }
    </script>

    <style>
        .main-content {
            padding-bottom: 120px; /* 为评论输入框留出空间 */
        }

        .back-btn, .share-btn {
            background: none;
            border: none;
            color: var(--text-dark);
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .back-btn:hover, .share-btn:hover {
            background: var(--border-dark);
        }

        .post-detail {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--border-dark);
        }

        .post-header {
            margin-bottom: 20px;
        }

        .post-title {
            font-size: 20px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .post-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .author-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .author-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
        }

        .author-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .author-name {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 14px;
        }

        .post-date {
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .post-stats {
            display: flex;
            gap: 15px;
        }

        .post-stats span {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .content-image {
            margin-bottom: 20px;
            border-radius: 10px;
            overflow: hidden;
        }

        .content-image img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .content-text {
            line-height: 1.6;
            color: var(--text-dark);
        }

        .content-text p {
            margin-bottom: 15px;
        }

        .content-text h3 {
            color: var(--accent-dark);
            margin: 20px 0 10px 0;
            font-size: 16px;
        }

        .post-actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid var(--border-dark);
        }

        .action-btn {
            flex: 1;
            background: var(--primary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 25px;
            padding: 10px 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-dark);
            font-size: 14px;
        }

        .action-btn:hover {
            border-color: var(--accent-dark);
        }

        .action-btn.active {
            background: var(--accent-dark);
            color: white;
            border-color: var(--accent-dark);
        }

        .comments-section {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid var(--border-dark);
        }

        .comments-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .comments-header h3 {
            color: var(--text-dark);
            margin: 0;
        }

        .sort-select {
            background: var(--primary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 8px;
            padding: 5px 10px;
            color: var(--text-dark);
            font-size: 12px;
        }

        .comment-item {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid var(--border-dark);
        }

        .comment-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .comment-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            overflow: hidden;
            flex-shrink: 0;
        }

        .comment-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .comment-content {
            flex: 1;
        }

        .comment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .comment-author {
            font-weight: 600;
            color: var(--text-dark);
            font-size: 14px;
        }

        .comment-time {
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .comment-text {
            color: var(--text-dark);
            line-height: 1.5;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .comment-actions {
            display: flex;
            gap: 15px;
        }

        .comment-like, .comment-reply {
            background: none;
            border: none;
            color: var(--text-secondary-dark);
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: color 0.3s ease;
        }

        .comment-like:hover, .comment-reply:hover {
            color: var(--accent-dark);
        }

        .load-more-comments {
            text-align: center;
            margin-top: 20px;
        }

        .comment-input-section {
            position: fixed;
            bottom: 80px;
            left: 50%;
            transform: translateX(-50%);
            width: 393px;
            background: var(--secondary-dark);
            border-top: 1px solid var(--border-dark);
            padding: 15px 20px;
        }

        .comment-input-container {
            display: flex;
            gap: 10px;
            align-items: center;
            margin-bottom: 8px;
        }

        .comment-input {
            flex: 1;
            background: var(--primary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 20px;
            padding: 10px 15px;
            color: var(--text-dark);
            font-size: 14px;
        }

        .comment-input:focus {
            outline: none;
            border-color: var(--accent-dark);
        }

        .send-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--accent-dark);
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .send-btn:disabled {
            background: var(--border-dark);
            cursor: not-allowed;
        }

        .send-btn:not(:disabled):hover {
            transform: scale(1.1);
        }

        .login-prompt {
            text-align: center;
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .login-prompt a {
            color: var(--accent-dark);
            text-decoration: none;
        }

        .share-options {
            display: flex;
            justify-content: space-around;
            padding: 20px 0;
        }

        .share-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            padding: 15px;
            border-radius: 10px;
            transition: background 0.3s ease;
        }

        .share-item:hover {
            background: var(--border-dark);
        }

        .share-item i {
            font-size: 24px;
            color: var(--accent-dark);
        }

        .share-item span {
            font-size: 12px;
            color: var(--text-dark);
        }
    </style>
</body>
</html>
