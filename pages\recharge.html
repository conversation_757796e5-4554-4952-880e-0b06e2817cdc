<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 充值</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info">充值</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 当前余额 -->
            <div class="balance-section">
                <div class="balance-header">
                    <i class="fas fa-wallet"></i>
                    <h2>账户余额</h2>
                </div>
                <div class="balance-amount">
                    <span class="currency">¥</span>
                    <span class="amount">156.00</span>
                </div>
            </div>

            <!-- 充值说明 -->
            <button class="btn btn-secondary info-btn" onclick="showRechargeInfo()">
                <i class="fas fa-info-circle"></i>
                充值说明
            </button>

            <!-- 在线充值 -->
            <div class="recharge-section">
                <h3 class="section-title">
                    <i class="fas fa-credit-card"></i>
                    在线充值
                </h3>
                <button class="btn btn-primary online-recharge-btn" onclick="goToOnlineRecharge()">
                    <i class="fas fa-external-link-alt"></i>
                    去充值
                </button>
                <p class="recharge-note">点击跳转到安全的第三方支付平台</p>
            </div>

            <!-- 充值卡充值 -->
            <div class="card-recharge-section">
                <h3 class="section-title">
                    <i class="fas fa-credit-card"></i>
                    充值卡充值
                </h3>
                
                <form class="recharge-form" onsubmit="handleCardRecharge(event)">
                    <div class="form-group">
                        <label for="card-number">充值卡号</label>
                        <div class="input-group">
                            <i class="fas fa-credit-card"></i>
                            <input type="text" id="card-number" class="form-input" placeholder="请输入充值卡号" required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="card-password">充值卡密码</label>
                        <div class="input-group">
                            <i class="fas fa-key"></i>
                            <input type="password" id="card-password" class="form-input" placeholder="请输入充值卡密码" required>
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary recharge-submit">
                        <i class="fas fa-plus-circle"></i>
                        立即充值
                    </button>
                </form>
            </div>

            <!-- 充值记录 -->
            <div class="record-section">
                <button class="btn btn-secondary record-btn" onclick="showRechargeRecord()">
                    <i class="fas fa-history"></i>
                    充值记录
                </button>
            </div>

            <!-- 快捷充值金额 -->
            <div class="quick-amounts">
                <h3 class="section-title">
                    <i class="fas fa-bolt"></i>
                    快捷金额
                </h3>
                <div class="amount-grid">
                    <div class="amount-item" onclick="selectQuickAmount(50)">
                        <span class="amount">¥50</span>
                        <span class="bonus">送¥5</span>
                    </div>
                    <div class="amount-item" onclick="selectQuickAmount(100)">
                        <span class="amount">¥100</span>
                        <span class="bonus">送¥10</span>
                    </div>
                    <div class="amount-item" onclick="selectQuickAmount(200)">
                        <span class="amount">¥200</span>
                        <span class="bonus">送¥25</span>
                    </div>
                    <div class="amount-item popular" onclick="selectQuickAmount(500)">
                        <span class="amount">¥500</span>
                        <span class="bonus">送¥80</span>
                        <div class="popular-badge">热门</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item active">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        function showRechargeInfo() {
            FashionApp.showModal('充值说明', `
                <div class="recharge-info">
                    <h3>充值方式说明</h3>
                    <div class="info-content">
                        <div class="info-item">
                            <h4>1. 在线充值</h4>
                            <p>支持支付宝、微信支付、银行卡等多种支付方式，安全便捷，实时到账。</p>
                        </div>
                        <div class="info-item">
                            <h4>2. 充值卡充值</h4>
                            <p>使用官方发行的充值卡进行充值，适合礼品赠送或线下购买。</p>
                        </div>
                        <div class="info-item">
                            <h4>3. 充值优惠</h4>
                            <p>充值满一定金额即可获得额外赠送，充值越多赠送越多。</p>
                        </div>
                        <div class="info-item">
                            <h4>4. 安全保障</h4>
                            <p>所有充值交易均采用SSL加密，确保您的资金安全。</p>
                        </div>
                        <div class="info-item">
                            <h4>5. 客服支持</h4>
                            <p>如遇充值问题，请联系客服：400-123-4567</p>
                        </div>
                    </div>
                </div>
            `);
        }

        function goToOnlineRecharge() {
            // 模拟跳转到外部充值网站
            FashionApp.showModal('跳转确认', `
                <div class="external-link-confirm">
                    <p>即将跳转到安全的第三方支付平台</p>
                    <div class="link-info">
                        <i class="fas fa-external-link-alt"></i>
                        <span>www.123.com</span>
                    </div>
                    <div class="confirm-actions">
                        <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                        <button class="btn btn-primary" onclick="confirmExternalLink()">确认跳转</button>
                    </div>
                </div>
            `);
        }

        function confirmExternalLink() {
            closeModal();
            FashionApp.showToast('正在跳转到充值页面...', 'info');
            // 这里可以实际跳转到外部链接
            // window.open('https://www.123.com', '_blank');
        }

        function togglePassword() {
            const passwordInput = document.getElementById('card-password');
            const toggleBtn = document.querySelector('.password-toggle i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }

        function handleCardRecharge(event) {
            event.preventDefault();
            
            const cardNumber = document.getElementById('card-number').value;
            const cardPassword = document.getElementById('card-password').value;
            
            if (!cardNumber || !cardPassword) {
                FashionApp.showToast('请填写完整的充值卡信息', 'error');
                return;
            }
            
            // 模拟充值验证
            if (cardNumber === '123456789' && cardPassword === '888888') {
                FashionApp.showToast('充值成功！已到账¥100', 'success');
                // 更新余额显示
                setTimeout(() => {
                    document.querySelector('.amount').textContent = '256.00';
                }, 1000);
            } else {
                FashionApp.showToast('充值卡号或密码错误', 'error');
            }
        }

        function showRechargeRecord() {
            FashionApp.showModal('充值记录', `
                <div class="recharge-record">
                    <div class="record-list">
                        <div class="record-item">
                            <div class="record-info">
                                <div class="record-type">在线充值</div>
                                <div class="record-time">2024-01-15 14:30</div>
                            </div>
                            <div class="record-amount success">+¥100.00</div>
                        </div>
                        <div class="record-item">
                            <div class="record-info">
                                <div class="record-type">充值卡充值</div>
                                <div class="record-time">2024-01-10 09:15</div>
                            </div>
                            <div class="record-amount success">+¥50.00</div>
                        </div>
                        <div class="record-item">
                            <div class="record-info">
                                <div class="record-type">在线充值</div>
                                <div class="record-time">2024-01-05 16:45</div>
                            </div>
                            <div class="record-amount success">+¥200.00</div>
                        </div>
                        <div class="record-item">
                            <div class="record-info">
                                <div class="record-type">充值卡充值</div>
                                <div class="record-time">2024-01-01 12:00</div>
                            </div>
                            <div class="record-amount success">+¥30.00</div>
                        </div>
                    </div>
                </div>
            `);
        }

        function selectQuickAmount(amount) {
            // 移除之前的选中状态
            document.querySelectorAll('.amount-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('selected');
            
            FashionApp.showToast(`已选择充值金额：¥${amount}`, 'info');
        }

        function closeModal() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }
    </script>

    <style>
        .balance-section {
            background: linear-gradient(135deg, var(--accent-dark), #ff5252);
            border-radius: 20px;
            padding: 30px 20px;
            text-align: center;
            margin-bottom: 20px;
            color: white;
        }

        .balance-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .balance-header i {
            font-size: 24px;
        }

        .balance-header h2 {
            margin: 0;
            font-size: 18px;
        }

        .balance-amount {
            font-size: 48px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
        }

        .currency {
            font-size: 24px;
            opacity: 0.8;
        }

        .info-btn {
            width: 100%;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .recharge-section {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--border-dark);
            text-align: center;
        }

        .online-recharge-btn {
            width: 100%;
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .recharge-note {
            font-size: 12px;
            color: var(--text-secondary-dark);
            margin: 0;
        }

        .card-recharge-section {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--border-dark);
        }

        .recharge-form {
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .input-group {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-group i {
            position: absolute;
            left: 15px;
            color: var(--text-secondary-dark);
            z-index: 1;
        }

        .input-group .form-input {
            padding-left: 45px;
            margin-bottom: 0;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            background: none;
            border: none;
            color: var(--text-secondary-dark);
            cursor: pointer;
            z-index: 1;
        }

        .recharge-submit {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .record-section {
            margin-bottom: 30px;
        }

        .record-btn {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .quick-amounts {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid var(--border-dark);
        }

        .amount-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
            margin-top: 15px;
        }

        .amount-item {
            background: var(--primary-dark);
            border: 2px solid var(--border-dark);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .amount-item:hover {
            border-color: var(--accent-dark);
            transform: translateY(-2px);
        }

        .amount-item.selected {
            border-color: var(--accent-dark);
            box-shadow: var(--glow-dark);
        }

        .amount-item.popular {
            background: linear-gradient(135deg, var(--primary-dark) 0%, rgba(255, 107, 107, 0.1) 100%);
        }

        .amount-item .amount {
            display: block;
            font-size: 24px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 5px;
        }

        .amount-item .bonus {
            font-size: 12px;
            color: var(--accent-dark);
            font-weight: 600;
        }

        .popular-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--accent-dark);
            color: white;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: bold;
        }

        /* 模态框内容样式 */
        .recharge-info .info-content {
            text-align: left;
        }

        .info-item {
            margin-bottom: 20px;
        }

        .info-item h4 {
            color: var(--accent-dark);
            margin-bottom: 8px;
        }

        .info-item p {
            color: var(--text-secondary-dark);
            line-height: 1.5;
            margin: 0;
        }

        .external-link-confirm {
            text-align: center;
        }

        .link-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            margin: 20px 0;
            padding: 15px;
            background: var(--secondary-dark);
            border-radius: 10px;
            border: 1px solid var(--border-dark);
        }

        .confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .recharge-record {
            max-height: 400px;
            overflow-y: auto;
        }

        .record-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-dark);
        }

        .record-item:last-child {
            border-bottom: none;
        }

        .record-type {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 5px;
        }

        .record-time {
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .record-amount {
            font-weight: bold;
            font-size: 16px;
        }

        .record-amount.success {
            color: #27ae60;
        }
    </style>
</body>
</html>
