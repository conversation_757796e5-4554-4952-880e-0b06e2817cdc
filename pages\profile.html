<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 我的</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info">我的</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 用户信息卡片 -->
            <div class="user-card">
                <div class="user-avatar">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80" alt="用户头像">
                    <div class="avatar-edit" onclick="editAvatar()">
                        <i class="fas fa-camera"></i>
                    </div>
                </div>
                <div class="user-info-section">
                    <h2 class="username">时尚达人小美</h2>
                    <div class="user-status">
                        <div class="vip-status">
                            <span class="vip-badge">超级VIP</span>
                            <button class="upgrade-btn" onclick="goToVIP()">
                                <i class="fas fa-crown"></i>
                                升级
                            </button>
                        </div>
                        <div class="balance-info">
                            <span class="balance-label">账户余额：</span>
                            <span class="balance-amount">¥156.00</span>
                            <button class="recharge-btn" onclick="goToRecharge()">
                                <i class="fas fa-plus"></i>
                                充值
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能菜单 -->
            <div class="menu-sections">
                <!-- 消息与收藏 -->
                <div class="menu-section">
                    <div class="menu-item" onclick="showMessages()">
                        <div class="menu-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="menu-content">
                            <span class="menu-title">站内信</span>
                            <span class="menu-desc">查看系统消息和通知</span>
                        </div>
                        <div class="menu-badge">3</div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="menu-item" onclick="showFavorites()">
                        <div class="menu-icon">
                            <i class="fas fa-heart"></i>
                        </div>
                        <div class="menu-content">
                            <span class="menu-title">我的收藏</span>
                            <span class="menu-desc">收藏的电子杂志</span>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- 财务管理 -->
                <div class="menu-section">
                    <div class="menu-item" onclick="showFinanceRecord()">
                        <div class="menu-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="menu-content">
                            <span class="menu-title">我的资金</span>
                            <span class="menu-desc">充值、消费记录</span>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="menu-item" onclick="showPurchaseHistory()">
                        <div class="menu-icon">
                            <i class="fas fa-shopping-bag"></i>
                        </div>
                        <div class="menu-content">
                            <span class="menu-title">我的购买</span>
                            <span class="menu-desc">已购买的电子期刊</span>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- 账户设置 -->
                <div class="menu-section">
                    <div class="menu-item" onclick="showProfileEdit()">
                        <div class="menu-icon">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <div class="menu-content">
                            <span class="menu-title">个人信息修改</span>
                            <span class="menu-desc">修改密码、个人资料</span>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="menu-item" onclick="showSubmission()">
                        <div class="menu-icon">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                        <div class="menu-content">
                            <span class="menu-title">我要投稿</span>
                            <span class="menu-desc">投稿办法与联系客服</span>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>

                <!-- 其他功能 -->
                <div class="menu-section">
                    <div class="menu-item" onclick="showSettings()">
                        <div class="menu-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <div class="menu-content">
                            <span class="menu-title">设置</span>
                            <span class="menu-desc">应用设置与偏好</span>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>

                    <div class="menu-item" onclick="showAbout()">
                        <div class="menu-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="menu-content">
                            <span class="menu-title">关于我们</span>
                            <span class="menu-desc">版本信息与帮助</span>
                        </div>
                        <div class="menu-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 退出登录 -->
            <div class="logout-section">
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    退出登录
                </button>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item active">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        function editAvatar() {
            FashionApp.showToast('更换头像功能', 'info');
        }

        function goToVIP() {
            FashionApp.showToast('跳转到VIP购买页面', 'info');
        }

        function goToRecharge() {
            FashionApp.showToast('跳转到充值页面', 'info');
        }

        function showMessages() {
            FashionApp.showModal('站内信', `
                <div class="messages-list">
                    <div class="message-item unread">
                        <div class="message-icon">
                            <i class="fas fa-bell"></i>
                        </div>
                        <div class="message-content">
                            <h4>系统通知</h4>
                            <p>您的VIP会员即将到期，请及时续费</p>
                            <span class="message-time">2小时前</span>
                        </div>
                    </div>
                    <div class="message-item unread">
                        <div class="message-icon">
                            <i class="fas fa-gift"></i>
                        </div>
                        <div class="message-content">
                            <h4>活动通知</h4>
                            <p>新春特惠活动开启，VIP会员8折优惠</p>
                            <span class="message-time">1天前</span>
                        </div>
                    </div>
                    <div class="message-item">
                        <div class="message-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="message-content">
                            <h4>新期发布</h4>
                            <p>时尚周刊第001期已发布，快来阅读吧</p>
                            <span class="message-time">3天前</span>
                        </div>
                    </div>
                </div>
            `);
        }

        function showFavorites() {
            FashionApp.showModal('我的收藏', `
                <div class="favorites-list">
                    <div class="favorite-item">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="收藏1">
                        <div class="favorite-info">
                            <h4>第001期 · 春日物语</h4>
                            <p>时尚周刊</p>
                        </div>
                    </div>
                    <div class="favorite-item">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="收藏2">
                        <div class="favorite-info">
                            <h4>第012期 · 都市夜色</h4>
                            <p>美丽世界</p>
                        </div>
                    </div>
                </div>
            `);
        }

        function showFinanceRecord() {
            FashionApp.showModal('我的资金', `
                <div class="finance-record">
                    <div class="record-item income">
                        <div class="record-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="record-info">
                            <h4>充值</h4>
                            <p>2024-01-15 14:30</p>
                        </div>
                        <div class="record-amount">+¥100.00</div>
                    </div>
                    <div class="record-item expense">
                        <div class="record-icon">
                            <i class="fas fa-minus"></i>
                        </div>
                        <div class="record-info">
                            <h4>购买VIP会员</h4>
                            <p>2024-01-10 09:15</p>
                        </div>
                        <div class="record-amount">-¥299.00</div>
                    </div>
                </div>
            `);
        }

        function showPurchaseHistory() {
            FashionApp.showModal('我的购买', `
                <div class="purchase-history">
                    <div class="purchase-item">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="购买1">
                        <div class="purchase-info">
                            <h4>第001期 · 春日物语</h4>
                            <p>购买时间：2024-01-15</p>
                            <span class="purchase-price">¥15.00</span>
                        </div>
                    </div>
                    <div class="purchase-item">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&q=80" alt="购买2">
                        <div class="purchase-info">
                            <h4>超级VIP会员</h4>
                            <p>购买时间：2024-01-10</p>
                            <span class="purchase-price">¥299.00</span>
                        </div>
                    </div>
                </div>
            `);
        }

        function showProfileEdit() {
            FashionApp.showModal('个人信息修改', `
                <div class="profile-edit">
                    <div class="edit-section">
                        <h4>基本信息</h4>
                        <div class="edit-item">
                            <label>用户名</label>
                            <input type="text" value="时尚达人小美" readonly>
                        </div>
                        <div class="edit-item">
                            <label>邮箱</label>
                            <input type="email" value="<EMAIL>">
                        </div>
                    </div>
                    <div class="edit-section">
                        <h4>密码修改</h4>
                        <button class="btn btn-secondary" onclick="changePassword()">修改密码</button>
                    </div>
                </div>
            `);
        }

        function showSubmission() {
            FashionApp.showModal('我要投稿', `
                <div class="submission-info">
                    <h3>投稿办法</h3>
                    <div class="submission-content">
                        <p>欢迎向时尚先锋摄影投稿！我们接受以下类型的作品：</p>
                        <ul>
                            <li>时尚摄影作品</li>
                            <li>模特写真</li>
                            <li>创意摄影</li>
                            <li>时尚文章</li>
                        </ul>
                        <p>投稿要求：</p>
                        <ul>
                            <li>原创作品，未在其他平台发布</li>
                            <li>高清图片，分辨率不低于2000px</li>
                            <li>附带作品说明和创作理念</li>
                        </ul>
                    </div>
                    <button class="btn btn-primary" onclick="contactEditor()">联系编辑</button>
                </div>
            `);
        }

        function showSettings() {
            FashionApp.showToast('设置页面', 'info');
        }

        function showAbout() {
            FashionApp.showModal('关于我们', `
                <div class="about-info">
                    <div class="app-info">
                        <h3>时尚先锋摄影</h3>
                        <p>版本：v1.0.0</p>
                        <p>专业的时尚摄影电子杂志平台</p>
                    </div>
                    <div class="contact-info">
                        <h4>联系我们</h4>
                        <p>客服电话：400-123-4567</p>
                        <p>客服邮箱：<EMAIL></p>
                        <p>官方网站：www.fashion.com</p>
                    </div>
                </div>
            `);
        }

        function changePassword() {
            FashionApp.showToast('跳转到密码修改页面', 'info');
        }

        function contactEditor() {
            FashionApp.showToast('联系编辑', 'info');
        }

        function logout() {
            FashionApp.showModal('确认退出', `
                <div class="logout-confirm">
                    <p>确定要退出登录吗？</p>
                    <div class="confirm-actions">
                        <button class="btn btn-secondary" onclick="closeModal()">取消</button>
                        <button class="btn btn-primary" onclick="confirmLogout()">确认退出</button>
                    </div>
                </div>
            `);
        }

        function confirmLogout() {
            FashionApp.showToast('已退出登录', 'success');
            closeModal();
        }

        function closeModal() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }
    </script>

    <style>
        .user-card {
            background: linear-gradient(135deg, var(--secondary-dark) 0%, rgba(255, 107, 107, 0.1) 100%);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid var(--border-dark);
            text-align: center;
        }

        .user-avatar {
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            border-radius: 50%;
            overflow: hidden;
            border: 3px solid var(--accent-dark);
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-edit {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 24px;
            height: 24px;
            background: var(--accent-dark);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            font-size: 12px;
        }

        .username {
            color: var(--text-dark);
            margin-bottom: 15px;
            font-size: 20px;
        }

        .user-status {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .vip-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .vip-badge {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #1a1a1a;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
        }

        .upgrade-btn {
            background: var(--accent-dark);
            color: white;
            border: none;
            border-radius: 15px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .upgrade-btn:hover {
            transform: translateY(-1px);
        }

        .balance-info {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 14px;
        }

        .balance-label {
            color: var(--text-secondary-dark);
        }

        .balance-amount {
            color: var(--accent-dark);
            font-weight: bold;
            font-size: 16px;
        }

        .recharge-btn {
            background: var(--secondary-dark);
            color: var(--text-dark);
            border: 1px solid var(--border-dark);
            border-radius: 15px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            transition: all 0.3s ease;
        }

        .recharge-btn:hover {
            border-color: var(--accent-dark);
        }

        .menu-sections {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 30px;
        }

        .menu-section {
            background: var(--secondary-dark);
            border-radius: 15px;
            border: 1px solid var(--border-dark);
            overflow: hidden;
        }

        .menu-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 18px 20px;
            cursor: pointer;
            transition: background 0.3s ease;
            border-bottom: 1px solid var(--border-dark);
        }

        .menu-item:last-child {
            border-bottom: none;
        }

        .menu-item:hover {
            background: var(--border-dark);
        }

        .menu-icon {
            width: 40px;
            height: 40px;
            background: var(--accent-dark);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
            flex-shrink: 0;
        }

        .menu-content {
            flex: 1;
        }

        .menu-title {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 4px;
        }

        .menu-desc {
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .menu-badge {
            background: var(--accent-dark);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .menu-arrow {
            color: var(--text-secondary-dark);
            font-size: 14px;
        }

        .logout-section {
            text-align: center;
        }

        .logout-btn {
            background: none;
            border: 2px solid var(--accent-dark);
            color: var(--accent-dark);
            border-radius: 25px;
            padding: 15px 40px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .logout-btn:hover {
            background: var(--accent-dark);
            color: white;
        }

        /* 模态框内容样式 */
        .messages-list,
        .favorites-list,
        .finance-record,
        .purchase-history {
            max-height: 400px;
            overflow-y: auto;
        }

        .message-item,
        .favorite-item,
        .record-item,
        .purchase-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-dark);
        }

        .message-item:last-child,
        .favorite-item:last-child,
        .record-item:last-child,
        .purchase-item:last-child {
            border-bottom: none;
        }

        .message-item.unread {
            background: rgba(255, 107, 107, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
        }

        .message-icon,
        .record-icon {
            width: 40px;
            height: 40px;
            background: var(--accent-dark);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .message-content,
        .favorite-info,
        .record-info,
        .purchase-info {
            flex: 1;
        }

        .message-content h4,
        .favorite-info h4,
        .record-info h4,
        .purchase-info h4 {
            color: var(--text-dark);
            margin-bottom: 5px;
            font-size: 14px;
        }

        .message-content p,
        .favorite-info p,
        .record-info p,
        .purchase-info p {
            color: var(--text-secondary-dark);
            font-size: 12px;
            margin-bottom: 5px;
        }

        .message-time {
            font-size: 11px;
            color: var(--text-secondary-dark);
        }

        .record-amount {
            font-weight: bold;
            font-size: 14px;
        }

        .record-item.income .record-amount {
            color: #27ae60;
        }

        .record-item.expense .record-amount {
            color: #e74c3c;
        }

        .purchase-price {
            color: var(--accent-dark);
            font-weight: bold;
            font-size: 12px;
        }

        .favorite-item img,
        .purchase-item img {
            width: 50px;
            height: 50px;
            border-radius: 8px;
            object-fit: cover;
        }

        .profile-edit .edit-section {
            margin-bottom: 20px;
        }

        .profile-edit h4 {
            color: var(--text-dark);
            margin-bottom: 15px;
        }

        .edit-item {
            margin-bottom: 15px;
        }

        .edit-item label {
            display: block;
            margin-bottom: 5px;
            color: var(--text-secondary-dark);
            font-size: 14px;
        }

        .edit-item input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-dark);
            border-radius: 8px;
            background: var(--primary-dark);
            color: var(--text-dark);
        }

        .submission-content ul {
            margin: 15px 0;
            padding-left: 20px;
        }

        .submission-content li {
            margin-bottom: 8px;
            color: var(--text-secondary-dark);
        }

        .about-info .app-info,
        .about-info .contact-info {
            margin-bottom: 20px;
        }

        .about-info h3,
        .about-info h4 {
            color: var(--text-dark);
            margin-bottom: 10px;
        }

        .about-info p {
            color: var(--text-secondary-dark);
            margin-bottom: 5px;
        }

        .logout-confirm {
            text-align: center;
        }

        .confirm-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    </style>
</body>
</html>
