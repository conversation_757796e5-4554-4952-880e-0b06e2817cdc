<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #ffffff;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #ff6b6b;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .nav-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .nav-card {
            background: #2d2d2d;
            border-radius: 15px;
            padding: 20px;
            border: 1px solid #404040;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .nav-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 50px rgba(255, 107, 107, 0.2);
            border-color: #ff6b6b;
        }

        .nav-card h3 {
            color: #ff6b6b;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .nav-card p {
            color: #cccccc;
            font-size: 14px;
            line-height: 1.5;
        }

        .status {
            background: #2d2d2d;
            border-radius: 10px;
            padding: 15px;
            border: 1px solid #404040;
            margin-bottom: 20px;
        }

        .status h3 {
            color: #ff6b6b;
            margin-bottom: 10px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .status-ok {
            color: #27ae60;
        }

        .status-error {
            color: #e74c3c;
        }

        .btn {
            background: #ff6b6b;
            color: white;
            border: none;
            border-radius: 25px;
            padding: 12px 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 600;
        }

        .btn:hover {
            background: #ff5252;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .test-section {
            margin-top: 30px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 时尚先锋摄影 - 导航测试</h1>
        
        <div class="status">
            <h3>📊 页面状态检查</h3>
            <div class="status-item">
                <span>首页 (home.html)</span>
                <span class="status-ok">✅ 已修复</span>
            </div>
            <div class="status-item">
                <span>登录页面 (login.html)</span>
                <span class="status-ok">✅ 可访问</span>
            </div>
            <div class="status-item">
                <span>VIP会员 (vip.html)</span>
                <span class="status-ok">✅ 可访问</span>
            </div>
            <div class="status-item">
                <span>充值页面 (recharge.html)</span>
                <span class="status-ok">✅ 可访问</span>
            </div>
            <div class="status-item">
                <span>社区吐槽 (community.html)</span>
                <span class="status-ok">✅ 可访问</span>
            </div>
            <div class="status-item">
                <span>杂志详情 (magazine-detail.html)</span>
                <span class="status-ok">✅ 可访问</span>
            </div>
        </div>

        <div class="nav-grid">
            <div class="nav-card" onclick="openPage('pages/home.html')">
                <h3>🏠 首页</h3>
                <p>主要功能入口，轮播图、功能按钮和电子杂志展示。现在支持真实页面跳转！</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/login.html')">
                <h3>🔐 登录页面</h3>
                <p>用户登录界面，支持多种登录方式和表单验证。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/register.html')">
                <h3>📝 注册页面</h3>
                <p>用户注册界面，包含完整的注册流程和安全问题设置。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/vip.html')">
                <h3>👑 VIP会员</h3>
                <p>VIP会员购买页面，展示不同会员套餐和特权对比。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/recharge.html')">
                <h3>💰 充值页面</h3>
                <p>账户充值功能，支持在线支付和充值卡充值。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/community.html')">
                <h3>💬 社区吐槽</h3>
                <p>社区帖子列表，用户可以浏览、搜索和筛选帖子。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/magazine-detail.html')">
                <h3>📖 杂志详情</h3>
                <p>电子杂志详情页面，展示杂志列表和购买选项。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/models.html')">
                <h3>👤 模特页面</h3>
                <p>模特列表页面，展示所有模特信息和作品。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/rankings.html')">
                <h3>🏆 热榜页面</h3>
                <p>热门内容排行榜，支持多种排行类型和时间筛选。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/profile.html')">
                <h3>👨‍💼 我的页面</h3>
                <p>个人中心页面，包含用户信息和功能菜单。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/search-results.html')">
                <h3>🔍 搜索结果</h3>
                <p>搜索结果展示页面，支持多种内容类型筛选。</p>
            </div>

            <div class="nav-card" onclick="openPage('pages/magazine-reader.html')">
                <h3>📱 杂志阅读</h3>
                <p>杂志阅读界面，支持图片和视频内容浏览。</p>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 测试说明</h3>
            <p style="color: #cccccc; margin-bottom: 20px;">
                点击上方任意卡片可以打开对应的页面进行测试。<br>
                首页现在已经修复，所有按钮都能正确跳转到对应页面！
            </p>
            <button class="btn" onclick="openPage('index.html')">
                📱 查看完整原型预览
            </button>
        </div>
    </div>

    <script>
        function openPage(url) {
            // 在新窗口中打开页面
            const newWindow = window.open(url, '_blank', 'width=393,height=852,scrollbars=yes,resizable=yes');
            
            // 如果无法打开新窗口，则在当前窗口打开
            if (!newWindow) {
                window.location.href = url;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎉 导航测试页面已加载');
            console.log('📱 所有页面链接已准备就绪');
            
            // 检查是否在移动设备上
            if (window.innerWidth <= 768) {
                document.body.style.padding = '10px';
                const navGrid = document.querySelector('.nav-grid');
                navGrid.style.gridTemplateColumns = '1fr';
            }
        });

        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'h' || e.key === 'H') {
                openPage('pages/home.html');
            } else if (e.key === 'i' || e.key === 'I') {
                openPage('index.html');
            }
        });
    </script>
</body>
</html>
