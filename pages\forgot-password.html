<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 密码找回</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info">密码找回</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="auth-container">
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="fas fa-key"></i>
                    </div>
                    <h2>找回密码</h2>
                    <p>通过安全问题验证身份，重置您的密码</p>
                </div>

                <!-- 步骤1：输入用户名 -->
                <div id="step1" class="reset-step active">
                    <form class="auth-form" onsubmit="handleStep1(event)">
                        <div class="form-group">
                            <label for="reset-username">用户名</label>
                            <div class="input-group">
                                <i class="fas fa-user"></i>
                                <input type="text" id="reset-username" class="form-input" placeholder="请输入您的用户名" required>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary auth-submit">
                            <i class="fas fa-arrow-right"></i>
                            下一步
                        </button>
                    </form>
                </div>

                <!-- 步骤2：安全问题验证 -->
                <div id="step2" class="reset-step">
                    <div class="security-info">
                        <div class="info-item">
                            <i class="fas fa-user"></i>
                            <span>用户名：<span id="display-username"></span></span>
                        </div>
                    </div>

                    <form class="auth-form" onsubmit="handleStep2(event)">
                        <div class="form-group">
                            <label for="security-question-display">安全问题</label>
                            <div class="question-display" id="security-question-display">
                                您的第一只宠物叫什么名字？
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="security-answer-input">请输入答案</label>
                            <div class="input-group">
                                <i class="fas fa-key"></i>
                                <input type="text" id="security-answer-input" class="form-input" placeholder="请输入安全问题的答案" required>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary auth-submit">
                            <i class="fas fa-check"></i>
                            验证答案
                        </button>
                    </form>

                    <div class="help-section">
                        <p class="help-text">
                            <i class="fas fa-exclamation-circle"></i>
                            如果您忘记了安全问题的答案，请联系客服
                        </p>
                        <button class="btn btn-secondary" onclick="contactCustomerService()">
                            <i class="fas fa-headset"></i>
                            联系客服
                        </button>
                    </div>
                </div>

                <!-- 步骤3：重置密码 -->
                <div id="step3" class="reset-step">
                    <div class="success-info">
                        <i class="fas fa-check-circle"></i>
                        <span>身份验证成功，请设置新密码</span>
                    </div>

                    <form class="auth-form" onsubmit="handleStep3(event)">
                        <div class="form-group">
                            <label for="new-password">新密码</label>
                            <div class="input-group">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="new-password" class="form-input" placeholder="请输入新密码" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('new-password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="confirm-new-password">确认新密码</label>
                            <div class="input-group">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="confirm-new-password" class="form-input" placeholder="请再次输入新密码" required>
                                <button type="button" class="password-toggle" onclick="togglePassword('confirm-new-password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="password-strength">
                            <div class="strength-label">密码强度：</div>
                            <div class="strength-bar">
                                <div class="strength-fill" id="strength-fill"></div>
                            </div>
                            <div class="strength-text" id="strength-text">弱</div>
                        </div>

                        <button type="submit" class="btn btn-primary auth-submit">
                            <i class="fas fa-save"></i>
                            重置密码
                        </button>
                    </form>
                </div>

                <div class="auth-links">
                    <a href="#" onclick="showLoginPage()" class="auth-link">
                        <i class="fas fa-arrow-left"></i>
                        返回登录
                    </a>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item active">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        function handleStep1(event) {
            event.preventDefault();
            
            const username = document.getElementById('reset-username').value;
            
            // 模拟查找用户
            if (username.trim() === '') {
                FashionApp.showToast('请输入用户名', 'error');
                return;
            }
            
            // 显示用户名
            document.getElementById('display-username').textContent = username;
            
            // 切换到步骤2
            showStep(2);
            FashionApp.showToast('找到用户，请回答安全问题', 'success');
        }
        
        function handleStep2(event) {
            event.preventDefault();
            
            const answer = document.getElementById('security-answer-input').value;
            
            // 模拟验证答案
            if (answer.trim() === '') {
                FashionApp.showToast('请输入答案', 'error');
                return;
            }
            
            // 这里应该验证答案是否正确
            // 模拟验证成功
            showStep(3);
            FashionApp.showToast('验证成功！', 'success');
        }
        
        function handleStep3(event) {
            event.preventDefault();
            
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-new-password').value;
            
            if (newPassword !== confirmPassword) {
                FashionApp.showToast('两次输入的密码不一致', 'error');
                return;
            }
            
            if (newPassword.length < 6) {
                FashionApp.showToast('密码长度至少6位', 'error');
                return;
            }
            
            // 模拟重置密码
            FashionApp.showToast('密码重置成功！', 'success');
            setTimeout(() => {
                showLoginPage();
            }, 2000);
        }
        
        function showStep(stepNumber) {
            // 隐藏所有步骤
            document.querySelectorAll('.reset-step').forEach(step => {
                step.classList.remove('active');
            });
            
            // 显示指定步骤
            document.getElementById(`step${stepNumber}`).classList.add('active');
        }
        
        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleBtn = passwordInput.parentElement.querySelector('.password-toggle i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleBtn.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleBtn.className = 'fas fa-eye';
            }
        }
        
        function contactCustomerService() {
            FashionApp.showModal('联系客服', `
                <div class="customer-service">
                    <p>如果您忘记了安全问题的答案，请通过以下方式联系我们：</p>
                    <div class="contact-methods">
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <span>客服电话：************</span>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <span>客服邮箱：<EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <i class="fab fa-qq"></i>
                            <span>QQ客服：123456789</span>
                        </div>
                    </div>
                    <p class="service-time">客服时间：周一至周日 9:00-21:00</p>
                </div>
            `);
        }
        
        function showLoginPage() {
            FashionApp.showToast('跳转到登录页面', 'info');
        }
        
        // 密码强度检测
        document.addEventListener('DOMContentLoaded', function() {
            const newPasswordInput = document.getElementById('new-password');
            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', checkPasswordStrength);
            }
        });
        
        function checkPasswordStrength() {
            const password = document.getElementById('new-password').value;
            const strengthFill = document.getElementById('strength-fill');
            const strengthText = document.getElementById('strength-text');
            
            let strength = 0;
            let strengthLabel = '弱';
            let strengthColor = '#e74c3c';
            
            if (password.length >= 6) strength += 1;
            if (password.match(/[a-z]/)) strength += 1;
            if (password.match(/[A-Z]/)) strength += 1;
            if (password.match(/[0-9]/)) strength += 1;
            if (password.match(/[^a-zA-Z0-9]/)) strength += 1;
            
            if (strength >= 4) {
                strengthLabel = '强';
                strengthColor = '#27ae60';
            } else if (strength >= 2) {
                strengthLabel = '中';
                strengthColor = '#f39c12';
            }
            
            strengthFill.style.width = `${(strength / 5) * 100}%`;
            strengthFill.style.background = strengthColor;
            strengthText.textContent = strengthLabel;
            strengthText.style.color = strengthColor;
        }
    </script>

    <style>
        .reset-step {
            display: none;
        }
        
        .reset-step.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        
        .security-info {
            background: var(--secondary-dark);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid var(--border-dark);
        }
        
        .info-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--text-secondary-dark);
        }
        
        .question-display {
            background: var(--secondary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 10px;
            padding: 15px;
            color: var(--text-dark);
            font-weight: 600;
        }
        
        .help-section {
            margin-top: 30px;
            text-align: center;
        }
        
        .help-text {
            color: var(--text-secondary-dark);
            font-size: 14px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .success-info {
            background: var(--secondary-dark);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #27ae60;
            color: #27ae60;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }
        
        .password-strength {
            margin: 15px 0;
        }
        
        .strength-label {
            font-size: 14px;
            color: var(--text-secondary-dark);
            margin-bottom: 8px;
        }
        
        .strength-bar {
            height: 6px;
            background: var(--border-dark);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 5px;
        }
        
        .strength-fill {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 3px;
        }
        
        .strength-text {
            font-size: 12px;
            font-weight: 600;
        }
        
        .customer-service {
            text-align: center;
        }
        
        .contact-methods {
            margin: 20px 0;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            color: var(--text-secondary-dark);
        }
        
        .service-time {
            font-size: 12px;
            color: var(--text-secondary-dark);
            margin-top: 15px;
        }
    </style>
</body>
</html>
