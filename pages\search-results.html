<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 搜索结果</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">
                <button class="back-btn" onclick="goBack()">
                    <i class="fas fa-arrow-left"></i>
                </button>
                搜索结果
            </div>
            <div class="user-info">
                <button class="filter-btn" onclick="showFilter()">
                    <i class="fas fa-filter"></i>
                </button>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 搜索框 -->
            <div class="search-container">
                <input type="text" class="search-input" placeholder="搜索期数、模特名..." value="春季" onkeyup="performSearch()">
                <i class="fas fa-search search-icon"></i>
            </div>

            <!-- 搜索信息 -->
            <div class="search-info">
                <div class="search-query">
                    搜索关键词：<span class="keyword">"春季"</span>
                </div>
                <div class="search-count">
                    找到 <span class="count">12</span> 个相关结果
                </div>
            </div>

            <!-- 筛选标签 -->
            <div class="filter-tags">
                <div class="filter-tag active" onclick="filterByType('all')">全部</div>
                <div class="filter-tag" onclick="filterByType('magazine')">杂志</div>
                <div class="filter-tag" onclick="filterByType('model')">模特</div>
                <div class="filter-tag" onclick="filterByType('theme')">主题</div>
            </div>

            <!-- 搜索结果列表 -->
            <div class="results-container">
                <div class="result-item magazine-result" data-type="magazine" onclick="openMagazine('2024001')">
                    <div class="result-image">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="春日物语">
                        <div class="result-badge">杂志</div>
                    </div>
                    <div class="result-content">
                        <h3 class="result-title">第001期 · 春日物语</h3>
                        <p class="result-desc">春天的温柔与浪漫，在镜头下绽放</p>
                        <div class="result-meta">
                            <span class="result-type">时尚周刊</span>
                            <span class="result-date">2024-01-15</span>
                        </div>
                        <div class="result-stats">
                            <span><i class="fas fa-eye"></i> 1.2k</span>
                            <span><i class="fas fa-heart"></i> 156</span>
                        </div>
                    </div>
                </div>

                <div class="result-item model-result" data-type="model" onclick="openModel('emily')">
                    <div class="result-image">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="艾米丽">
                        <div class="result-badge">模特</div>
                    </div>
                    <div class="result-content">
                        <h3 class="result-title">艾米丽 (Emily)</h3>
                        <p class="result-desc">专业时尚模特，擅长春季清新风格拍摄</p>
                        <div class="result-meta">
                            <span class="result-type">专业模特</span>
                            <span class="result-works">作品 8 期</span>
                        </div>
                        <div class="result-stats">
                            <span><i class="fas fa-star"></i> 4.8</span>
                            <span><i class="fas fa-users"></i> 2.3k</span>
                        </div>
                    </div>
                </div>

                <div class="result-item magazine-result" data-type="magazine" onclick="openMagazine('2023008')">
                    <div class="result-image">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="春季时尚">
                        <div class="result-badge">杂志</div>
                    </div>
                    <div class="result-content">
                        <h3 class="result-title">第008期 · 春季时尚指南</h3>
                        <p class="result-desc">2023年春季最新时尚趋势解析</p>
                        <div class="result-meta">
                            <span class="result-type">风格指南</span>
                            <span class="result-date">2023-03-20</span>
                        </div>
                        <div class="result-stats">
                            <span><i class="fas fa-eye"></i> 890</span>
                            <span><i class="fas fa-heart"></i> 234</span>
                        </div>
                    </div>
                </div>

                <div class="result-item theme-result" data-type="theme" onclick="openTheme('spring-collection')">
                    <div class="result-image">
                        <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="春季系列">
                        <div class="result-badge">主题</div>
                    </div>
                    <div class="result-content">
                        <h3 class="result-title">春季系列专题</h3>
                        <p class="result-desc">汇集所有春季主题的精彩作品</p>
                        <div class="result-meta">
                            <span class="result-type">主题合集</span>
                            <span class="result-count">包含 15 期</span>
                        </div>
                        <div class="result-stats">
                            <span><i class="fas fa-images"></i> 120+</span>
                            <span><i class="fas fa-bookmark"></i> 567</span>
                        </div>
                    </div>
                </div>

                <div class="result-item magazine-result" data-type="magazine" onclick="openMagazine('2023005')">
                    <div class="result-image">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="春日写真">
                        <div class="result-badge">杂志</div>
                    </div>
                    <div class="result-content">
                        <h3 class="result-title">第005期 · 春日写真集</h3>
                        <p class="result-desc">记录春天里最美的瞬间</p>
                        <div class="result-meta">
                            <span class="result-type">摄影艺术</span>
                            <span class="result-date">2023-04-10</span>
                        </div>
                        <div class="result-stats">
                            <span><i class="fas fa-eye"></i> 1.5k</span>
                            <span><i class="fas fa-heart"></i> 345</span>
                        </div>
                    </div>
                </div>

                <div class="result-item model-result" data-type="model" onclick="openModel('sophia')">
                    <div class="result-image">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&q=80" alt="索菲亚">
                        <div class="result-badge">模特</div>
                    </div>
                    <div class="result-content">
                        <h3 class="result-title">索菲亚 (Sophia)</h3>
                        <p class="result-desc">国际知名模特，春季大片御用模特</p>
                        <div class="result-meta">
                            <span class="result-type">国际模特</span>
                            <span class="result-works">作品 12 期</span>
                        </div>
                        <div class="result-stats">
                            <span><i class="fas fa-star"></i> 4.9</span>
                            <span><i class="fas fa-users"></i> 5.2k</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more">
                <button class="btn btn-secondary" onclick="loadMoreResults()">
                    <i class="fas fa-plus"></i>
                    加载更多结果
                </button>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        function goBack() {
            FashionApp.showToast('返回上一页', 'info');
        }

        function showFilter() {
            FashionApp.showModal('筛选选项', `
                <div class="filter-options">
                    <h3>筛选条件</h3>
                    <div class="filter-section">
                        <h4>内容类型</h4>
                        <div class="filter-checkboxes">
                            <label><input type="checkbox" checked> 杂志</label>
                            <label><input type="checkbox" checked> 模特</label>
                            <label><input type="checkbox" checked> 主题</label>
                        </div>
                    </div>
                    <div class="filter-section">
                        <h4>发布时间</h4>
                        <div class="filter-checkboxes">
                            <label><input type="radio" name="time" checked> 全部时间</label>
                            <label><input type="radio" name="time"> 最近一周</label>
                            <label><input type="radio" name="time"> 最近一月</label>
                            <label><input type="radio" name="time"> 最近一年</label>
                        </div>
                    </div>
                    <div class="filter-section">
                        <h4>排序方式</h4>
                        <div class="filter-checkboxes">
                            <label><input type="radio" name="sort" checked> 相关度</label>
                            <label><input type="radio" name="sort"> 发布时间</label>
                            <label><input type="radio" name="sort"> 热度</label>
                        </div>
                    </div>
                    <div class="filter-actions">
                        <button class="btn btn-secondary" onclick="resetFilter()">重置</button>
                        <button class="btn btn-primary" onclick="applyFilter()">应用筛选</button>
                    </div>
                </div>
            `);
        }

        function performSearch() {
            const searchTerm = document.querySelector('.search-input').value;
            if (searchTerm.trim()) {
                document.querySelector('.keyword').textContent = `"${searchTerm}"`;
                FashionApp.showToast(`搜索: ${searchTerm}`, 'info');
            }
        }

        function filterByType(type) {
            // 更新标签状态
            document.querySelectorAll('.filter-tag').forEach(tag => {
                tag.classList.remove('active');
            });
            event.target.classList.add('active');

            // 筛选结果
            const results = document.querySelectorAll('.result-item');
            results.forEach(result => {
                if (type === 'all') {
                    result.style.display = 'block';
                } else {
                    const resultType = result.getAttribute('data-type');
                    if (resultType === type) {
                        result.style.display = 'block';
                    } else {
                        result.style.display = 'none';
                    }
                }
            });

            // 更新结果数量
            const visibleResults = document.querySelectorAll('.result-item[style="display: block"], .result-item:not([style*="display: none"])');
            document.querySelector('.count').textContent = visibleResults.length;
        }

        function openMagazine(magazineId) {
            FashionApp.showToast(`打开杂志: ${magazineId}`, 'info');
        }

        function openModel(modelId) {
            FashionApp.showToast(`查看模特: ${modelId}`, 'info');
        }

        function openTheme(themeId) {
            FashionApp.showToast(`查看主题: ${themeId}`, 'info');
        }

        function loadMoreResults() {
            FashionApp.showToast('加载更多搜索结果...', 'info');
        }

        function resetFilter() {
            FashionApp.showToast('重置筛选条件', 'info');
        }

        function applyFilter() {
            FashionApp.showToast('应用筛选条件', 'success');
            closeModal();
        }

        function closeModal() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }
    </script>

    <style>
        .back-btn, .filter-btn {
            background: none;
            border: none;
            color: var(--text-dark);
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }

        .back-btn:hover, .filter-btn:hover {
            background: var(--border-dark);
        }

        .search-info {
            margin-bottom: 20px;
            padding: 15px;
            background: var(--secondary-dark);
            border-radius: 10px;
            border: 1px solid var(--border-dark);
        }

        .search-query {
            margin-bottom: 8px;
            color: var(--text-dark);
            font-size: 14px;
        }

        .keyword {
            color: var(--accent-dark);
            font-weight: bold;
        }

        .search-count {
            color: var(--text-secondary-dark);
            font-size: 12px;
        }

        .count {
            color: var(--accent-dark);
            font-weight: bold;
        }

        .filter-tags {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto;
            padding-bottom: 5px;
        }

        .filter-tag {
            padding: 8px 16px;
            background: var(--secondary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            white-space: nowrap;
        }

        .filter-tag.active {
            background: var(--accent-dark);
            color: white;
            border-color: var(--accent-dark);
        }

        .filter-tag:hover {
            border-color: var(--accent-dark);
        }

        .results-container {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-bottom: 20px;
        }

        .result-item {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 15px;
            border: 1px solid var(--border-dark);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            gap: 15px;
        }

        .result-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-dark);
        }

        .result-image {
            position: relative;
            width: 80px;
            height: 80px;
            border-radius: 10px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .result-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .result-badge {
            position: absolute;
            top: 5px;
            right: 5px;
            background: var(--accent-dark);
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: bold;
        }

        .result-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .result-title {
            font-size: 16px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 5px;
            line-height: 1.3;
        }

        .result-desc {
            font-size: 14px;
            color: var(--text-secondary-dark);
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .result-meta {
            display: flex;
            gap: 10px;
            margin-bottom: 8px;
        }

        .result-type,
        .result-date,
        .result-works,
        .result-count {
            font-size: 12px;
            color: var(--text-secondary-dark);
            background: var(--primary-dark);
            padding: 2px 6px;
            border-radius: 6px;
        }

        .result-stats {
            display: flex;
            gap: 15px;
        }

        .result-stats span {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--text-secondary-dark);
        }

        .result-stats i {
            font-size: 12px;
        }

        .load-more {
            text-align: center;
            margin-top: 20px;
        }

        .load-more .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        /* 模态框筛选样式 */
        .filter-options {
            max-width: 300px;
        }

        .filter-section {
            margin-bottom: 20px;
        }

        .filter-section h4 {
            color: var(--text-dark);
            margin-bottom: 10px;
            font-size: 14px;
        }

        .filter-checkboxes {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .filter-checkboxes label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--text-secondary-dark);
            cursor: pointer;
        }

        .filter-checkboxes input {
            margin: 0;
        }

        .filter-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        /* 不同类型结果的特殊样式 */
        .magazine-result .result-badge {
            background: #3498db;
        }

        .model-result .result-badge {
            background: #e74c3c;
        }

        .theme-result .result-badge {
            background: #9b59b6;
        }
    </style>
</body>
</html>
