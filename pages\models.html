<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 模特</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info">模特</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 搜索框 -->
            <div class="search-container">
                <input type="text" class="search-input" placeholder="输入模特名称搜索..." onkeyup="searchModels()">
                <i class="fas fa-search search-icon"></i>
            </div>

            <!-- 筛选标签 -->
            <div class="filter-tags">
                <div class="filter-tag active" onclick="filterModels('all')">全部</div>
                <div class="filter-tag" onclick="filterModels('international')">国际模特</div>
                <div class="filter-tag" onclick="filterModels('domestic')">国内模特</div>
                <div class="filter-tag" onclick="filterModels('new')">新人模特</div>
            </div>

            <!-- 模特列表 -->
            <div class="models-container">
                <div class="model-card international" onclick="openModelDetail('emily')">
                    <div class="model-image">
                        <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="艾米丽">
                        <div class="model-badge international">国际</div>
                        <div class="model-rating">
                            <i class="fas fa-star"></i>
                            <span>4.9</span>
                        </div>
                    </div>
                    <div class="model-info">
                        <h3 class="model-name">艾米丽 (Emily)</h3>
                        <p class="model-specialty">清新自然风格，春季主题专家</p>
                        <div class="model-stats">
                            <div class="stat-item">
                                <i class="fas fa-camera"></i>
                                <span>往期作品共 8 期</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span>2.3k 关注</span>
                            </div>
                        </div>
                        <div class="model-tags">
                            <span class="tag">清新</span>
                            <span class="tag">自然</span>
                            <span class="tag">春季</span>
                        </div>
                    </div>
                </div>

                <div class="model-card international" onclick="openModelDetail('sophia')">
                    <div class="model-image">
                        <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="索菲亚">
                        <div class="model-badge international">国际</div>
                        <div class="model-rating">
                            <i class="fas fa-star"></i>
                            <span>4.8</span>
                        </div>
                    </div>
                    <div class="model-info">
                        <h3 class="model-name">索菲亚 (Sophia)</h3>
                        <p class="model-specialty">都市时尚，夜景拍摄专业模特</p>
                        <div class="model-stats">
                            <div class="stat-item">
                                <i class="fas fa-camera"></i>
                                <span>往期作品共 12 期</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span>5.2k 关注</span>
                            </div>
                        </div>
                        <div class="model-tags">
                            <span class="tag">都市</span>
                            <span class="tag">时尚</span>
                            <span class="tag">夜景</span>
                        </div>
                    </div>
                </div>

                <div class="model-card domestic" onclick="openModelDetail('victoria')">
                    <div class="model-image">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="维多利亚">
                        <div class="model-badge domestic">国内</div>
                        <div class="model-rating">
                            <i class="fas fa-star"></i>
                            <span>4.7</span>
                        </div>
                    </div>
                    <div class="model-info">
                        <h3 class="model-name">维多利亚 (Victoria)</h3>
                        <p class="model-specialty">复古风情，经典造型演绎专家</p>
                        <div class="model-stats">
                            <div class="stat-item">
                                <i class="fas fa-camera"></i>
                                <span>往期作品共 6 期</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span>1.8k 关注</span>
                            </div>
                        </div>
                        <div class="model-tags">
                            <span class="tag">复古</span>
                            <span class="tag">经典</span>
                            <span class="tag">优雅</span>
                        </div>
                    </div>
                </div>

                <div class="model-card domestic" onclick="openModelDetail('isabella')">
                    <div class="model-image">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="伊莎贝拉">
                        <div class="model-badge domestic">国内</div>
                        <div class="model-rating">
                            <i class="fas fa-star"></i>
                            <span>4.6</span>
                        </div>
                    </div>
                    <div class="model-info">
                        <h3 class="model-name">伊莎贝拉 (Isabella)</h3>
                        <p class="model-specialty">自然风光，户外拍摄经验丰富</p>
                        <div class="model-stats">
                            <div class="stat-item">
                                <i class="fas fa-camera"></i>
                                <span>往期作品共 5 期</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span>1.5k 关注</span>
                            </div>
                        </div>
                        <div class="model-tags">
                            <span class="tag">自然</span>
                            <span class="tag">户外</span>
                            <span class="tag">清纯</span>
                        </div>
                    </div>
                </div>

                <div class="model-card new" onclick="openModelDetail('alice')">
                    <div class="model-image">
                        <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="爱丽丝">
                        <div class="model-badge new">新人</div>
                        <div class="model-rating">
                            <i class="fas fa-star"></i>
                            <span>4.5</span>
                        </div>
                    </div>
                    <div class="model-info">
                        <h3 class="model-name">爱丽丝 (Alice)</h3>
                        <p class="model-specialty">青春活力，校园风格新星</p>
                        <div class="model-stats">
                            <div class="stat-item">
                                <i class="fas fa-camera"></i>
                                <span>往期作品共 3 期</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span>890 关注</span>
                            </div>
                        </div>
                        <div class="model-tags">
                            <span class="tag">青春</span>
                            <span class="tag">活力</span>
                            <span class="tag">校园</span>
                        </div>
                    </div>
                </div>

                <div class="model-card new" onclick="openModelDetail('luna')">
                    <div class="model-image">
                        <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="露娜">
                        <div class="model-badge new">新人</div>
                        <div class="model-rating">
                            <i class="fas fa-star"></i>
                            <span>4.4</span>
                        </div>
                    </div>
                    <div class="model-info">
                        <h3 class="model-name">露娜 (Luna)</h3>
                        <p class="model-specialty">神秘气质，夜景人像专业</p>
                        <div class="model-stats">
                            <div class="stat-item">
                                <i class="fas fa-camera"></i>
                                <span>往期作品共 2 期</span>
                            </div>
                            <div class="stat-item">
                                <i class="fas fa-users"></i>
                                <span>567 关注</span>
                            </div>
                        </div>
                        <div class="model-tags">
                            <span class="tag">神秘</span>
                            <span class="tag">夜景</span>
                            <span class="tag">气质</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 加载更多 -->
            <div class="load-more">
                <button class="btn btn-secondary" onclick="loadMoreModels()">
                    <i class="fas fa-plus"></i>
                    查看更多模特
                </button>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item" onclick="FashionApp.goToHome()">
                <span>首页</span>
            </div>
            <div class="nav-item active">
                <span>模特</span>
            </div>
            <div class="nav-item" onclick="FashionApp.goToRankings()">
                <span>热榜</span>
            </div>
            <div class="nav-item" onclick="FashionApp.goToProfile()">
                <span>我的</span>
            </div>
        </div>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        function searchModels() {
            const searchTerm = document.querySelector('.search-input').value.toLowerCase();
            const models = document.querySelectorAll('.model-card');

            models.forEach(model => {
                const name = model.querySelector('.model-name').textContent.toLowerCase();
                const specialty = model.querySelector('.model-specialty').textContent.toLowerCase();
                const tags = Array.from(model.querySelectorAll('.tag')).map(tag => tag.textContent.toLowerCase()).join(' ');

                if (name.includes(searchTerm) || specialty.includes(searchTerm) || tags.includes(searchTerm)) {
                    model.style.display = 'block';
                } else {
                    model.style.display = 'none';
                }
            });
        }

        function filterModels(category) {
            // 更新标签状态
            document.querySelectorAll('.filter-tag').forEach(tag => {
                tag.classList.remove('active');
            });
            event.target.classList.add('active');

            // 筛选模特
            const models = document.querySelectorAll('.model-card');
            models.forEach(model => {
                if (category === 'all') {
                    model.style.display = 'block';
                } else {
                    if (model.classList.contains(category)) {
                        model.style.display = 'block';
                    } else {
                        model.style.display = 'none';
                    }
                }
            });
        }

        function openModelDetail(modelId) {
            FashionApp.showToast(`查看模特详情: ${modelId}`, 'info');
            // 这里可以跳转到模特详情页面
        }

        function loadMoreModels() {
            FashionApp.showToast('加载更多模特...', 'info');
        }
    </script>

    <style>
        .filter-tags {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto;
            padding-bottom: 5px;
        }

        .filter-tag {
            padding: 8px 16px;
            background: var(--secondary-dark);
            border: 1px solid var(--border-dark);
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            white-space: nowrap;
        }

        .filter-tag.active {
            background: var(--accent-dark);
            color: white;
            border-color: var(--accent-dark);
        }

        .filter-tag:hover {
            border-color: var(--accent-dark);
        }

        .models-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 20px;
        }

        .model-card {
            background: var(--secondary-dark);
            border-radius: 20px;
            overflow: hidden;
            border: 1px solid var(--border-dark);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .model-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-dark);
        }

        .model-image {
            position: relative;
            height: 200px;
            overflow: hidden;
        }

        .model-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .model-card:hover .model-image img {
            transform: scale(1.05);
        }

        .model-badge {
            position: absolute;
            top: 15px;
            left: 15px;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }

        .model-badge.international {
            background: #3498db;
        }

        .model-badge.domestic {
            background: #e74c3c;
        }

        .model-badge.new {
            background: #27ae60;
        }

        .model-rating {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 6px 10px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            font-weight: bold;
        }

        .model-rating i {
            color: #ffd700;
        }

        .model-info {
            padding: 20px;
        }

        .model-name {
            font-size: 18px;
            font-weight: bold;
            color: var(--text-dark);
            margin-bottom: 8px;
        }

        .model-specialty {
            font-size: 14px;
            color: var(--text-secondary-dark);
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .model-stats {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-bottom: 15px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
            color: var(--text-secondary-dark);
        }

        .stat-item i {
            color: var(--accent-dark);
            width: 16px;
        }

        .model-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .tag {
            background: var(--primary-dark);
            color: var(--text-secondary-dark);
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            border: 1px solid var(--border-dark);
        }

        .load-more {
            text-align: center;
            margin-top: 20px;
        }

        .load-more .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .model-stats {
                gap: 6px;
            }

            .stat-item {
                font-size: 12px;
            }

            .model-name {
                font-size: 16px;
            }

            .model-specialty {
                font-size: 13px;
            }
        }
    </style>
</body>
</html>
