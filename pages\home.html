<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时尚先锋摄影 - 首页</title>
    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body data-theme="dark">
    <div class="phone-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <i class="fas fa-battery-three-quarters"></i>
            </div>
        </div>

        <!-- 顶部导航 -->
        <div class="top-nav">
            <div class="logo">时尚先锋摄影</div>
            <div class="user-info" onclick="showLoginModal()">登录</div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 轮播图 -->
            <div class="carousel">
                <div class="carousel-container">
                    <div class="carousel-slide">
                        <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="时尚先锋">
                        <div class="carousel-text">时尚先锋</div>
                    </div>
                    <div class="carousel-slide">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="时尚先锋">
                        <div class="carousel-text">时尚先锋</div>
                    </div>
                    <div class="carousel-slide">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80" alt="时尚先锋">
                        <div class="carousel-text">时尚先锋</div>
                    </div>
                </div>
                <div class="carousel-dots">
                    <div class="carousel-dot active"></div>
                    <div class="carousel-dot"></div>
                    <div class="carousel-dot"></div>
                </div>
            </div>

            <!-- 功能按钮区域 -->
            <div class="function-buttons">
                <div class="button-row">
                    <div class="function-btn" onclick="goToVIP()">
                        <i class="fas fa-crown"></i>
                        <span>VIP会员</span>
                    </div>
                    <div class="function-btn" onclick="goToCommunity()">
                        <i class="fas fa-comments"></i>
                        <span>社区与吐槽</span>
                    </div>
                </div>
                <div class="function-btn-large" onclick="goToRecharge()">
                    <i class="fas fa-credit-card"></i>
                    <span>充值</span>
                </div>
            </div>

            <!-- 电子杂志区域 -->
            <div class="magazine-section">
                <h2 class="section-title">
                    <i class="fas fa-book-open"></i>
                    电子杂志
                </h2>
                <div class="magazine-grid">
                    <div class="magazine-cover" onclick="goToMagazineDetail('fashion-weekly')">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="时尚周刊">
                        <div class="magazine-title">时尚周刊</div>
                    </div>
                    <div class="magazine-cover" onclick="goToMagazineDetail('beauty-world')">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="美丽世界">
                        <div class="magazine-title">美丽世界</div>
                    </div>
                    <div class="magazine-cover" onclick="goToMagazineDetail('model-life')">
                        <img src="https://images.unsplash.com/photo-1469334031218-e382a71b716b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="模特生活">
                        <div class="magazine-title">模特生活</div>
                    </div>
                    <div class="magazine-cover" onclick="goToMagazineDetail('photo-art')">
                        <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="摄影艺术">
                        <div class="magazine-title">摄影艺术</div>
                    </div>
                    <div class="magazine-cover" onclick="goToMagazineDetail('style-guide')">
                        <img src="https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="风格指南">
                        <div class="magazine-title">风格指南</div>
                    </div>
                    <div class="magazine-cover" onclick="goToMagazineDetail('luxury-fashion')">
                        <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" alt="奢华时尚">
                        <div class="magazine-title">奢华时尚</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active">
                <span>首页</span>
            </div>
            <div class="nav-item">
                <span>模特</span>
            </div>
            <div class="nav-item">
                <span>热榜</span>
            </div>
            <div class="nav-item">
                <span>我的</span>
            </div>
        </div>

        <!-- 客服悬浮按钮 -->
        <button class="floating-btn" onclick="goToCustomerService()">
            <i class="fas fa-headset"></i>
        </button>
    </div>

    <!-- 主题切换按钮 -->
    <button class="theme-toggle" onclick="FashionApp.toggleTheme()">🌙</button>

    <script src="../js/app.js"></script>
    <script>
        function showLoginModal() {
            FashionApp.showModal('用户登录', `
                <form class="login-form">
                    <input type="text" class="form-input" placeholder="用户名" required>
                    <input type="password" class="form-input" placeholder="密码" required>
                    <button type="submit" class="btn btn-primary">登录</button>
                </form>
            `);
        }

        function goToVIP() {
            FashionApp.showToast('跳转到VIP会员页面', 'info');
        }

        function goToCommunity() {
            FashionApp.showToast('跳转到社区吐槽页面', 'info');
        }

        function goToRecharge() {
            FashionApp.showToast('跳转到充值页面', 'info');
        }

        function goToMagazineDetail(magazineId) {
            FashionApp.showToast(`打开杂志: ${magazineId}`, 'info');
        }

        function goToCustomerService() {
            FashionApp.showToast('联系客服', 'info');
        }
    </script>

    <style>
        .carousel-text {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
        }

        .function-buttons {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .button-row {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .function-btn {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-dark);
        }

        .function-btn:hover {
            background: var(--accent-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-dark);
        }

        .function-btn i {
            font-size: 24px;
            color: var(--accent-dark);
        }

        .function-btn:hover i {
            color: white;
        }

        .function-btn span {
            font-weight: 600;
        }

        .function-btn-large {
            background: var(--secondary-dark);
            border-radius: 15px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid var(--border-dark);
            height: 100%;
        }

        .function-btn-large:hover {
            background: var(--accent-dark);
            transform: translateY(-2px);
            box-shadow: var(--shadow-dark);
        }

        .function-btn-large i {
            font-size: 32px;
            color: var(--accent-dark);
        }

        .function-btn-large:hover i {
            color: white;
        }

        .function-btn-large span {
            font-weight: 600;
            font-size: 16px;
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 20px;
            font-size: 20px;
            font-weight: bold;
            color: var(--text-dark);
        }

        .section-title i {
            color: var(--accent-dark);
        }

        .magazine-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
        }
    </style>
</body>
</html>
